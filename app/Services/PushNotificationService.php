<?php

namespace App\Services;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Facades\Log;

class PushNotificationService
{
    private $client;
    private $expoApiUrl = 'https://exp.host/--/api/v2/push/send';

    public function __construct()
    {
        $this->client = new Client([
            'timeout' => 30,
            'headers' => [
                'Accept' => 'application/json',
                'Content-Type' => 'application/json',
            ]
        ]);
    }

    /**
     * Send push notification to single device
     */
    public function sendToDevice(string $token, string $title, string $body, array $data = []): bool
    {
        return $this->sendToMultiple([$token], $title, $body, $data);
    }

    /**
     * Send push notification to multiple devices
     */
    public function sendToMultiple(array $tokens, string $title, string $body, array $data = []): bool
    {
        // Filter valid Expo tokens
        $validTokens = array_filter($tokens, function($token) {
            return $this->isValidExpoToken($token);
        });

        if (empty($validTokens)) {
            Log::warning('No valid Expo tokens provided');
            return false;
        }

        $messages = [];
        foreach ($validTokens as $token) {
            $messages[] = [
                'to' => $token,
                'title' => $title,
                'body' => $body,
                'data' => $data,
                'sound' => 'default',
                'badge' => 1,
                'priority' => 'high',
                'channelId' => 'default',
            ];
        }

        try {
            $response = $this->client->post($this->expoApiUrl, [
                'json' => $messages
            ]);

            $result = json_decode($response->getBody()->getContents(), true);
            
            Log::info('Push notification sent', [
                'tokens_count' => count($validTokens),
                'response' => $result
            ]);

            return $this->handleExpoResponse($result);
        } catch (RequestException $e) {
            Log::error('Failed to send push notification', [
                'error' => $e->getMessage(),
                'tokens' => $validTokens
            ]);
            return false;
        }
    }

    /**
     * Send notification to user (fetch tokens from database)
     */
    public function sendToUser(int $userId, string $title, string $body, array $data = []): bool
    {
        // Assuming you have a users table with fcm_token column
        $user = \App\Models\User::find($userId);
        
        if (!$user || !$user->fcm_token) {
            Log::warning("No FCM token found for user {$userId}");
            return false;
        }

        return $this->sendToDevice($user->fcm_token, $title, $body, $data);
    }

    /**
     * Send notification to all users
     */
    public function sendToAllUsers(string $title, string $body, array $data = []): bool
    {
        $tokens = \App\Models\User::whereNotNull('fcm_token')
            ->pluck('fcm_token')
            ->toArray();

        if (empty($tokens)) {
            Log::warning('No users with FCM tokens found');
            return false;
        }

        return $this->sendToMultiple($tokens, $title, $body, $data);
    }

    /**
     * Validate Expo push token format
     */
    private function isValidExpoToken(string $token): bool
    {
        return preg_match('/^ExponentPushToken\[[a-zA-Z0-9_-]+\]$/', $token) === 1;
    }

    /**
     * Handle Expo API response and check for errors
     */
    private function handleExpoResponse(array $response): bool
    {
        $hasErrors = false;

        if (isset($response['data'])) {
            foreach ($response['data'] as $result) {
                if (isset($result['status']) && $result['status'] === 'error') {
                    Log::error('Expo push notification error', [
                        'error' => $result['message'] ?? 'Unknown error',
                        'details' => $result['details'] ?? null
                    ]);
                    $hasErrors = true;
                }
            }
        }

        return !$hasErrors;
    }
}