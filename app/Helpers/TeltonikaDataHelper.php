<?php

/**
 * Get odometer value based on device type with fallback logic
 */
function getOdometerValue($data)
{
    // FMC650: 192, 216
    if (isset($data['192']) && $data['192']) return (int)$data['192'];
    if (isset($data['216']) && $data['216']) return (int)$data['216'];

    // FMC250: 87
    if (isset($data['87']) && $data['87']) return (int)$data['87'];

    // FMC250: 16 | FMC130: 16 | FMC800: 16
    if (isset($data['16']) && $data['16']) return (int)$data['16'];

    return 0;
}

/**
 * Get fuel consumption in liters based on device type with fallback logic
 */
function getFuelConsumption($data)
{
    // FMC650: 86
    if (isset($data['86']) && $data['86']) return (float)$data['86'];

    // FMC250: 83
    if (isset($data['83']) && $data['83']) return (float)$data['83'];

    return 0;
}

/**
 * Get fuel level percentage based on device type with fallback logic
 */
function getFuelLevelPercentage($data)
{
    // FMC250: 89
    if (isset($data['89']) && $data['89']) return (float)$data['89'];

    // FMC650: 87
    if (isset($data['87']) && $data['87']) return (float)$data['87'];


    return 0;
}

/**
 * Get fuel level liters based on device type with fallback logic
 */
function getFuelLevelLiters($data)
{
    // FMC650: 91
    if (isset($data['91']) && $data['91']) return (float)$data['91'];

    // FMC250: 84
    if (isset($data['84']) && $data['84']) return (float)$data['84'];

    return 0;
}

/**
 * Get Speed
 */

function getSpeed($data)
{
    // FMC650: 80
    if (isset($data['80']) && $data['80']) return $data['80'];
    if (isset($data['191']) && $data['191']) return $data['191'];

    // FMC250: 81
    if (isset($data['81']) && $data['81']) return $data['81'];

    // default
    return $data['speed'] ?? 0;
}

/**
 * Get iButton status
 */

function getiButtonStatus($data)
{
    // FMC250: 248
    if (isset($data['248']) && $data['248']) return $data['248'];

    // FMC650: 251
    if (isset($data['251']) && $data['251']) return $data['251'];

    // default
    return null;
}

/**
 * Get average fuel consumption (per distance)
 * 
 * @param float|int $totalFuel      Total fuel used (liters)
 * @param float|int $totalDistance  Total distance traveled (kilometers)
 * @param string    $unit           Unit of measurement: 'l_per_km', 'l_per_100km', or 'km_per_l'
 * @return float|null               Average fuel consumption, or null if distance is zero
 */
function getAverageFuelValue($totalFuel, $totalDistance, $unit = 'l_per_km')
{
    if ($totalDistance <= 0) {
        return null; // Avoid division by zero
    }

    switch ($unit) {
        case 'l_per_km':
            // Liters per kilometer
            return $totalFuel / $totalDistance;

        case 'l_per_100km':
            // Liters per 100 kilometers
            return ($totalFuel / $totalDistance) * 100;

        case 'km_per_l':
            // Kilometers per liter (efficiency)
            return $totalDistance / $totalFuel;

        default:
            throw new InvalidArgumentException("Invalid unit: $unit");
    }
}


/**
 * Validate and sanitize Teltonika data
 */
function validateTeltonikaData($data)
{
    if (!is_array($data)) {
        return false;
    }

    // Check for required fields
    $requiredFields = ['latitude', 'longitude', 'last_update'];
    foreach ($requiredFields as $field) {
        if (!isset($data[$field]) || empty($data[$field])) {
            return false;
        }
    }

    // Validate coordinates
    $lat = (float)$data['latitude'];
    $lng = (float)$data['longitude'];

    if ($lat < -90 || $lat > 90 || $lng < -180 || $lng > 180) {
        return false;
    }

    return true;
}

/**
 * Get device status indicators for dynamic display
 */
function getDeviceStatusIndicators($data)
{
    return [
        'ignition' => [
            'value' => isset($data['239']) ? (int)$data['239'] : null,
            'available' => isset($data['239'])
        ],
        'movement' => [
            'value' => isset($data['240']) ? (int)$data['240'] : null,
            'available' => isset($data['240'])
        ],
        'speed' => [
            'value' => isset($data['speed']) ? (float)$data['speed'] : null,
            'available' => isset($data['speed'])
        ],
        'battery_level' => [
            'value' => isset($data['67']) ? (int)$data['67'] : (isset($data['68']) ? (int)$data['68'] : null),
            'available' => isset($data['67']) || isset($data['68'])
        ],
        'signal_strength' => [
            'value' => isset($data['21']) ? (int)$data['21'] : null,
            'available' => isset($data['21'])
        ],
        'fuel_level' => [
            'value' => getFuelLevelLiters($data),
            'available' => getFuelLevelLiters($data) > 0
        ],
        'odometer' => [
            'value' => getOdometerValue($data),
            'available' => getOdometerValue($data) > 0
        ]
    ];
}

/**
 * Get odometer value by specific parameter ID
 */
function getOdometerValueByParameter($data, $parameterId)
{
    if (isset($data[$parameterId]) && $data[$parameterId]) {
        return (int)$data[$parameterId];
    }
    return 0;
}

/**
 * Get all available odometer parameter IDs in priority order
 */
function getOdometerParameterIds()
{
    return ['192', '216', '87', '16'];
}

/**
 * Get fuel consumption by specific parameter ID
 */
function getFuelConsumptionByParameter($data, $parameterId)
{
    if (isset($data[$parameterId]) && $data[$parameterId]) {
        return (float)$data[$parameterId];
    }
    return 0;
}

/**
 * Get all available fuel consumption parameter IDs in priority order
 */
function getFuelConsumptionParameterIds()
{
    return ['86', '83'];
}
