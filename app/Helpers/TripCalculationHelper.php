<?php

namespace App\Helpers;

use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class TripCalculationHelper
{
    // Minimum thresholds for meaningful trips and stops
    const MIN_TRIP_DURATION_MINUTES = 2;
    const MIN_STOP_DURATION_MINUTES = 2;
    const MIN_TRIP_DISTANCE_KM = 0.05;
    const MIN_SPEED_FOR_TRIP = 2; // km/h

    // State consolidation thresholds to prevent duplicate trips/stops
    const MIN_STATE_DURATION_MINUTES = 3; // Minimum time to consider a state change valid
    const MAX_CONSOLIDATION_DISTANCE_KM = 0.5; // Maximum distance to consolidate nearby stops/trips
    const MAX_CONSOLIDATION_TIME_MINUTES = 10; // Maximum time gap to consolidate states

    /**
     * Calculate trips and stops from history data using eventID 250 as primary method
     *
     * @param array $historyData The GPS history data for the day
     * @param string $date The date in d-m-Y format
     * @return array Contains 'stops' and 'trips' arrays
     */
    public static function calculateTripsAndStops($historyData, $date)
    {
        $stops = [];
        $trips = [];
        $firstValidData = null;
        $lastValidData = null;
        $tripStartOdometer = null;
        $tripStartFuel = null;

        // State tracking variables
        $currentState = null; // 'trip' or 'stop'
        $currentStateStart = null; // When current state started
        $currentStateLocation = null; // Location data for current state
        $lastEventStatus = null; // Last eventID 250 status (0 or 1)

        // Parameter tracking for consistent odometer/fuel calculations
        $currentOdometerParameter = null; // Which parameter ID to use for current trip (192, 216, 87, 16)
        $lastKnownOdometerValue = null; // Last known odometer value for current parameter
        $currentFuelParameter = null; // Which parameter ID to use for current trip (86, 83)
        $lastKnownFuelValue = null; // Last known fuel value for current parameter

        // Check if we have any eventID 250 events in the data
        $hasEvent250 = self::hasEvent250InData($historyData);

        // Determine which odometer and fuel parameters are available in the whole dataset
        $availableOdometerParams = self::detectAvailableOdometerParameters($historyData);
        $availableFuelParams = self::detectAvailableFuelParameters($historyData);

        foreach ($historyData as $data) {
            // Skip invalid data
            if (!isset($data['latitude']) || !isset($data['longitude'])) {
                continue;
            }

            // Keep track of first and last valid data for overall trip metrics
            if (!$firstValidData) {
                $firstValidData = $data;

                // Initialize parameter tracking based on available parameters
                $currentOdometerParameter = $availableOdometerParams['primary'];
                $currentFuelParameter = $availableFuelParams['primary'];

                $tripStartOdometer = self::getOdometerValueByParameter($data, $currentOdometerParameter);
                $tripStartFuel = self::getFuelValueByParameter($data, $currentFuelParameter);

                $lastKnownOdometerValue = $tripStartOdometer;
                $lastKnownFuelValue = $tripStartFuel;

                // Initialize state based on first record
                $currentState = self::determineInitialState($data, $hasEvent250);
                $timestamp = parseFlexibleTimestamp($data['last_update']);

                if ($currentState === 'stop') {
                    // Vehicle was stopped at beginning of day
                    $dayStart = $timestamp->copy()->startOfDay();
                    $currentStateStart = $dayStart;
                    $currentStateLocation = [
                        'timestamp' => $dayStart,
                        'latitude' => $data['latitude'],
                        'longitude' => $data['longitude'],
                        'odometer' => $lastKnownOdometerValue,
                        'fuel' => $lastKnownFuelValue,
                        'odometer_param' => $currentOdometerParameter,
                        'fuel_param' => $currentFuelParameter
                    ];
                } else {
                    // Vehicle was moving at beginning of day
                    $currentStateStart = $timestamp;
                    $currentStateLocation = [
                        'timestamp' => $timestamp,
                        'latitude' => $data['latitude'],
                        'longitude' => $data['longitude'],
                        'odometer' => $lastKnownOdometerValue,
                        'fuel' => $lastKnownFuelValue,
                        'odometer_param' => $currentOdometerParameter,
                        'fuel_param' => $currentFuelParameter
                    ];
                }

                // Set initial event status if available
                if (isset($data['eventID']) && $data['eventID'] == 250 && isset($data['250'])) {
                    $lastEventStatus = (int)$data['250'];
                }
            }
            $lastValidData = $data;

            // Parse current record values
            $timestamp = parseFlexibleTimestamp($data['last_update']);
            $speed = isset($data['speed']) ? (float)$data['speed'] : 0;
            $movement = isset($data['240']) ? (int)$data['240'] : 0;

            // Get odometer and fuel values using consistent parameters
            $currentOdometer = self::getOdometerValueByParameter($data, $currentOdometerParameter);
            $currentFuel = self::getFuelValueByParameter($data, $currentFuelParameter);

            // Update last known values if current values are available
            if ($currentOdometer > 0) {
                $lastKnownOdometerValue = $currentOdometer;
            } else {
                // Use last known value if current is not available
                $currentOdometer = $lastKnownOdometerValue;
            }

            if ($currentFuel > 0) {
                $lastKnownFuelValue = $currentFuel;
            } else {
                // Use last known value if current is not available
                $currentFuel = $lastKnownFuelValue;
            }

            // Process state changes based on eventID 250 or fallback logic
            $newState = null;

            if ($hasEvent250 && isset($data['eventID']) && $data['eventID'] == 250 && isset($data['250'])) {
                // Primary method: Use eventID 250
                $currentEventStatus = (int)$data['250'];

                if ($currentEventStatus === 1) {
                    $newState = 'trip';
                } elseif ($currentEventStatus === 0) {
                    $newState = 'stop';
                }

                $lastEventStatus = $currentEventStatus;
            } elseif (!$hasEvent250) {
                // Fallback method: Use speed and ignition only if no eventID 250 exists
                if ($speed < 1 && $movement === 0) {
                    $newState = 'stop';
                } elseif ($speed >= self::MIN_SPEED_FOR_TRIP) {
                    $newState = 'trip';
                }
            }

            // Process state transitions with validation to prevent brief state changes
            if ($newState && $newState !== $currentState) {
                // OPTIMIZATION: Check if current state has been active long enough to be valid
                $currentStateDuration = $currentStateStart ? $currentStateStart->diffInMinutes($timestamp) : 0;
                $shouldProcessStateChange = true;

                // If current state is very short, it might be a false state change
                if ($currentStateDuration < self::MIN_STATE_DURATION_MINUTES && $currentStateStart) {
                    // Check if we should ignore this brief state and continue with the new state
                    $shouldProcessStateChange = self::shouldProcessBriefStateChange(
                        $currentState,
                        $newState,
                        $currentStateDuration,
                        $currentStateLocation,
                        $data
                    );
                }

                if ($shouldProcessStateChange) {
                    // State changed - finalize previous state and start new one
                    if ($currentState === 'trip' && $currentStateStart && $currentStateLocation) {
                        // End trip
                        $tripData = self::createTripData(
                            $currentStateStart,
                            $timestamp,
                            $currentStateLocation,
                            $data
                        );

                        if ($tripData) {
                            $trips[] = $tripData;
                        }
                    } elseif ($currentState === 'stop' && $currentStateStart && $currentStateLocation) {
                        // End stop
                        $stopData = self::createStopData(
                            $currentStateStart,
                            $timestamp,
                            $currentStateLocation,
                            $currentOdometer,
                            $currentFuel,
                            $tripStartFuel
                        );

                        if ($stopData) {
                            $stops[] = $stopData;
                        }
                    }

                    // Start new state
                    $currentState = $newState;
                    $currentStateStart = $timestamp;
                    $currentStateLocation = [
                        'timestamp' => $timestamp,
                        'latitude' => $data['latitude'],
                        'longitude' => $data['longitude'],
                        'odometer' => $currentOdometer,
                        'fuel' => $currentFuel,
                        'odometer_param' => $currentOdometerParameter,
                        'fuel_param' => $currentFuelParameter
                    ];
                } else {
                    // Ignore brief state change - continue with new state but keep original start time
                    $currentState = $newState;
                    // Keep the original $currentStateStart and $currentStateLocation
                }
            }
        }

        // Handle ongoing state at end of day
        $reportDate = Carbon::createFromFormat('d-m-Y', $date);
        $endOfDay = $reportDate->copy()->endOfDay();
        $currentTime = $reportDate->isToday() ? now() : $endOfDay;

        if ($currentState && $currentStateStart && $currentStateLocation) {
            if ($currentState === 'trip') {
                // Handle ongoing trip
                $tripData = self::createOngoingTripData(
                    $currentStateStart,
                    $currentTime,
                    $currentStateLocation,
                    $lastValidData
                );

                if ($tripData) {
                    $trips[] = $tripData;
                }
            } elseif ($currentState === 'stop') {
                // Handle ongoing stop
                $stopData = self::createOngoingStopData(
                    $currentStateLocation,
                    $currentStateStart,
                    $lastValidData,
                    $currentTime,
                    $tripStartFuel
                );

                if ($stopData) {
                    $stops[] = $stopData;
                }
            }
        }

        // OPTIMIZATION: Create proper sequence by merging all events and sorting by time
        $allEvents = self::createProperSequence($stops, $trips);

        return [
            'stops' => $allEvents['stops'],
            'trips' => $allEvents['trips'],
            'sequence' => $allEvents['sequence'], // New: proper chronological sequence
            'firstValidData' => $firstValidData,
            'lastValidData' => $lastValidData,
            'tripStartOdometer' => $tripStartOdometer,
            'tripStartFuel' => $tripStartFuel
        ];
    }

    /**
     * Check if history data contains eventID 250 events
     */
    private static function hasEvent250InData($historyData)
    {
        foreach ($historyData as $data) {
            if (isset($data['eventID']) && $data['eventID'] == 250 && isset($data['250'])) {
                return true;
            }
        }
        return false;
    }

    /**
     * Determine initial vehicle state based on first record
     */
    private static function determineInitialState($data, $hasEvent250)
    {
        if ($hasEvent250) {
            // If we have eventID 250 events, only use them for state determination
            if (isset($data['eventID']) && $data['eventID'] == 250 && isset($data['250'])) {
                return (int)$data['250'] === 1 ? 'trip' : 'stop';
            }
            // If first record doesn't have eventID 250, assume stopped (conservative approach)
            return 'stop';
        } else {
            // Fallback to speed/ignition logic only if no eventID 250 exists
            $speed = isset($data['speed']) ? (float)$data['speed'] : 0;
            $ignition = isset($data['239']) ? (int)$data['239'] : 0;

            if ($speed < 1 && $ignition === 1) {
                return 'stop';
            } elseif ($speed >= self::MIN_SPEED_FOR_TRIP) {
                return 'trip';
            }

            // Default to stop if unclear
            return 'stop';
        }
    }

    /**
     * Create trip data structure
     */
    private static function createTripData($startTime, $endTime, $startLocation, $endData)
    {
        $tripDuration = $startTime->diffForHumans($endTime, true);
        $tripDurationMinutes = $startTime->diffInMinutes($endTime);

        // Apply minimum thresholds for meaningful trips
        if ($tripDurationMinutes < self::MIN_TRIP_DURATION_MINUTES || !$startLocation) {
            return null;
        }

        // Calculate trip distance - prioritize odometer readings for accuracy
        $tripDistance = 0;

        // First try odometer using consistent parameter (most accurate for actual distance traveled)
        if (isset($startLocation['odometer']) && isset($startLocation['odometer_param'])) {
            $startOdo = $startLocation['odometer'];
            $endOdo = self::getOdometerValueByParameter($endData, $startLocation['odometer_param']);

            // If end odometer is not available with the same parameter, use the start odometer value
            if ($endOdo <= 0) {
                $endOdo = $startOdo;
            }

            $tripDistance = self::calculateTripDistance($startOdo, $endOdo);
        }

        // If odometer distance is 0 or unavailable, fallback to GPS straight-line distance
        if ($tripDistance <= 0) {
            $tripDistance = self::calculateGpsDistance(
                $startLocation['latitude'],
                $startLocation['longitude'],
                $endData['latitude'],
                $endData['longitude']
            );
        }

        // Apply minimum distance threshold
        if ($tripDistance < self::MIN_TRIP_DISTANCE_KM) {
            return null;
        }

        // Calculate fuel consumption - prioritize actual fuel sensor readings using consistent parameter
        $fuelConsumption = 0;
        if (isset($startLocation['fuel']) && isset($startLocation['fuel_param'])) {
            $startFuel = $startLocation['fuel'];
            $endFuel = self::getFuelValueByParameter($endData, $startLocation['fuel_param']);

            // If end fuel is not available with the same parameter, use the start fuel value
            if ($endFuel <= 0) {
                $endFuel = $startFuel;
            }

            // Calculate fuel consumption - if end fuel is less than start fuel,
            // it means fuel was consumed (normal case)
            if ($endFuel < $startFuel) {
                $fuelConsumption = $startFuel - $endFuel;
            } elseif ($endFuel > $startFuel) {
                // If end fuel is greater, it might be a refill during the trip
                // Use distance-based estimation for fuel consumption
                $fuelConsumption = $tripDistance * 0.08; // Estimate 0.08L per km (more realistic)
            } else {
                // If fuel levels are equal, use distance-based estimation
                $fuelConsumption = $tripDistance * 0.08; // Estimate 0.08L per km
            }
        } else {
            // No fuel data available, use distance-based estimation
            $fuelConsumption = $tripDistance * 0.08; // Estimate 0.08L per km
        }

        return [
            'start_time' => $startTime,
            'end_time' => $endTime,
            'duration' => $tripDuration,
            'duration_minutes' => $tripDurationMinutes,
            'start_location' => [
                'lat' => $startLocation['latitude'],
                'lng' => $startLocation['longitude'],
                'address' => getAddressFromCoordinates($startLocation['latitude'], $startLocation['longitude'])
            ],
            'end_location' => [
                'lat' => $endData['latitude'],
                'lng' => $endData['longitude'],
                'address' => getAddressFromCoordinates($endData['latitude'], $endData['longitude'])
            ],
            'distance' => $tripDistance,
            'fuel_consumption' => $fuelConsumption
        ];
    }

    /**
     * Create stop data structure
     */
    private static function createStopData($startTime, $endTime, $stopLocation, $currentOdometer, $currentFuel, $tripStartFuel)
    {
        $duration = $startTime->diffForHumans($endTime, true);
        $durationMinutes = $startTime->diffInMinutes($endTime);

        // Apply minimum threshold for meaningful stops
        if ($durationMinutes < self::MIN_STOP_DURATION_MINUTES) {
            return null;
        }

        return [
            'time' => $startTime->format('H:i'),
            'start_time' => $startTime,
            'end_time' => $endTime,
            'duration' => $duration,
            'location' => [
                'lat' => $stopLocation['latitude'],
                'lng' => $stopLocation['longitude'],
                'address' => getAddressFromCoordinates($stopLocation['latitude'], $stopLocation['longitude'])
            ],
            'odometer' => $currentOdometer,
            'fuel' => $currentFuel,
            'trip_distance' => self::calculateTripDistance($stopLocation['odometer'], $currentOdometer), // Distance since stop in km
            'trip_fuel' => max(0, abs($tripStartFuel - $currentFuel)) // Absolute difference in fuel
        ];
    }

    /**
     * Create ongoing stop data structure
     */
    private static function createOngoingStopData($stopLocation, $stopStartTime, $lastValidData, $currentTime, $tripStartFuel)
    {
        $stopTimestamp = $stopStartTime instanceof Carbon ? $stopStartTime : Carbon::parse($stopStartTime);
        $duration = $stopTimestamp->diffForHumans($currentTime, true);
        $durationMinutes = $stopTimestamp->diffInMinutes($currentTime);

        // Apply minimum threshold for meaningful stops
        if ($durationMinutes < self::MIN_STOP_DURATION_MINUTES) {
            return null;
        }

        // Use consistent parameters for odometer and fuel calculations
        $currentOdometer = isset($stopLocation['odometer_param']) ?
            self::getOdometerValueByParameter($lastValidData, $stopLocation['odometer_param']) :
            getOdometerValue($lastValidData);

        $currentFuel = isset($stopLocation['fuel_param']) ?
            self::getFuelValueByParameter($lastValidData, $stopLocation['fuel_param']) :
            getFuelConsumption($lastValidData);

        return [
            'time' => $stopTimestamp->format('H:i'),
            'start_time' => $stopTimestamp,
            'end_time' => Carbon::createFromFormat('d-m-Y', $stopTimestamp->format('d-m-Y'))->endOfDay(),
            'duration' => $duration,
            'location' => [
                'lat' => $stopLocation['latitude'],
                'lng' => $stopLocation['longitude'],
                'address' => getAddressFromCoordinates($stopLocation['latitude'], $stopLocation['longitude'])
            ],
            'odometer' => $currentOdometer,
            'fuel' => $currentFuel,
            'trip_distance' => self::calculateTripDistance($stopLocation['odometer'], $currentOdometer), // Distance since stop in km
            'trip_fuel' => max(0, abs($tripStartFuel - $currentFuel)) // Absolute difference in fuel
        ];
    }

    /**
     * Create ongoing trip data structure
     */
    private static function createOngoingTripData($currentTripStart, $currentTime, $tripStartLocation, $lastValidData)
    {
        $tripDuration = $currentTripStart->diffForHumans($currentTime, true);
        $tripDurationMinutes = $currentTripStart->diffInMinutes($currentTime);

        // Apply minimum thresholds for meaningful trips
        if ($tripDurationMinutes < self::MIN_TRIP_DURATION_MINUTES || !$tripStartLocation) {
            return null;
        }

        // Calculate trip distance - prioritize odometer readings for accuracy using consistent parameter
        $tripDistance = 0;

        // First try odometer using consistent parameter (most accurate for actual distance traveled)
        if (isset($tripStartLocation['odometer']) && isset($tripStartLocation['odometer_param'])) {
            $startOdo = $tripStartLocation['odometer'];
            $endOdo = self::getOdometerValueByParameter($lastValidData, $tripStartLocation['odometer_param']);

            // If end odometer is not available with the same parameter, use the start odometer value
            if ($endOdo <= 0) {
                $endOdo = $startOdo;
            }

            $tripDistance = self::calculateTripDistance($startOdo, $endOdo);
        }

        // If odometer distance is 0 or unavailable, fallback to GPS straight-line distance
        if ($tripDistance <= 0) {
            $tripDistance = self::calculateGpsDistance(
                $tripStartLocation['latitude'],
                $tripStartLocation['longitude'],
                $lastValidData['latitude'],
                $lastValidData['longitude']
            );
        }

        // Apply minimum distance threshold
        if ($tripDistance < self::MIN_TRIP_DISTANCE_KM) {
            return null;
        }

        // Calculate fuel consumption - prioritize actual fuel sensor readings using consistent parameter
        $fuelConsumption = 0;
        if (isset($tripStartLocation['fuel']) && isset($tripStartLocation['fuel_param'])) {
            $startFuel = $tripStartLocation['fuel'];
            $endFuel = self::getFuelValueByParameter($lastValidData, $tripStartLocation['fuel_param']);

            // If end fuel is not available with the same parameter, use the start fuel value
            if ($endFuel <= 0) {
                $endFuel = $startFuel;
            }

            // Calculate fuel consumption - if end fuel is less than start fuel,
            // it means fuel was consumed (normal case)
            if ($endFuel < $startFuel) {
                $fuelConsumption = $startFuel - $endFuel;
            } elseif ($endFuel > $startFuel) {
                // If end fuel is greater, it might be a refill during the trip
                // Use distance-based estimation for fuel consumption
                $fuelConsumption = $tripDistance * 0.08; // Estimate 0.08L per km (more realistic)
            } else {
                // If fuel levels are equal, use distance-based estimation
                $fuelConsumption = $tripDistance * 0.08; // Estimate 0.08L per km
            }
        } else {
            // No fuel data available, use distance-based estimation
            $fuelConsumption = $tripDistance * 0.08; // Estimate 0.08L per km
        }

        return [
            'start_time' => $currentTripStart,
            'end_time' => $currentTime,
            'duration' => $tripDuration,
            'duration_minutes' => $tripDurationMinutes,
            'start_location' => [
                'lat' => $tripStartLocation['latitude'],
                'lng' => $tripStartLocation['longitude'],
                'address' => getAddressFromCoordinates($tripStartLocation['latitude'], $tripStartLocation['longitude'])
            ],
            'end_location' => [
                'lat' => $lastValidData['latitude'],
                'lng' => $lastValidData['longitude'],
                'address' => getAddressFromCoordinates($lastValidData['latitude'], $lastValidData['longitude'])
            ],
            'distance' => $tripDistance,
            'fuel_consumption' => $fuelConsumption
        ];
    }

    /**
     * Calculate distance between two GPS coordinates using Haversine formula
     */
    private static function calculateGpsDistance($lat1, $lon1, $lat2, $lon2)
    {
        if (!$lat1 || !$lon1 || !$lat2 || !$lon2) {
            return 0;
        }

        // Convert latitude and longitude from degrees to radians
        $lat1 = deg2rad((float)$lat1);
        $lon1 = deg2rad((float)$lon1);
        $lat2 = deg2rad((float)$lat2);
        $lon2 = deg2rad((float)$lon2);

        // Haversine formula
        $dlat = $lat2 - $lat1;
        $dlon = $lon2 - $lon1;
        $a = sin($dlat / 2) * sin($dlat / 2) + cos($lat1) * cos($lat2) * sin($dlon / 2) * sin($dlon / 2);
        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));
        $distance = 6371 * $c; // Earth radius in km

        return $distance;
    }

    /**
     * Calculate trip distance in km
     */
    private static function calculateTripDistance($startOdometer, $endOdometer)
    {
        if (!$startOdometer || !$endOdometer) {
            return 0;
        }

        // Calculate the difference and convert to km
        $distanceMeters = max(0, $endOdometer - $startOdometer);

        // If the distance is unreasonably large (over 1000 km), it might be an odometer reset
        if ($distanceMeters > 1000000) { // 1000 km in meters
            return 0;
        }

        return $distanceMeters / 1000; // Convert to km
    }

    /**
     * Detect which odometer parameters are available in the whole dataset
     * Returns the primary parameter to use and fallback options
     */
    private static function detectAvailableOdometerParameters($historyData)
    {
        $parameterPriority = ['192', '216', '87', '16']; // FMS -> CAN -> Basic
        $availableParams = [];

        // Check which parameters exist in the dataset
        foreach ($historyData as $data) {
            foreach ($parameterPriority as $param) {
                if (isset($data[$param]) && $data[$param] > 0) {
                    $availableParams[$param] = true;
                }
            }
        }

        // Find the highest priority parameter that's available
        $primaryParam = null;
        foreach ($parameterPriority as $param) {
            if (isset($availableParams[$param])) {
                $primaryParam = $param;
                break;
            }
        }

        return [
            'primary' => $primaryParam ?: '16', // Default to basic parameter
            'available' => array_keys($availableParams),
            'priority' => $parameterPriority
        ];
    }

    /**
     * Detect which fuel parameters are available in the whole dataset
     * Returns the primary parameter to use and fallback options
     */
    private static function detectAvailableFuelParameters($historyData)
    {
        $parameterPriority = ['86', '83']; // FMC650 -> FMC250
        $availableParams = [];

        // Check which parameters exist in the dataset
        foreach ($historyData as $data) {
            foreach ($parameterPriority as $param) {
                if (isset($data[$param]) && $data[$param] > 0) {
                    $availableParams[$param] = true;
                }
            }
        }

        // Find the highest priority parameter that's available
        $primaryParam = null;
        foreach ($parameterPriority as $param) {
            if (isset($availableParams[$param])) {
                $primaryParam = $param;
                break;
            }
        }

        return [
            'primary' => $primaryParam ?: '86', // Default to FMC650 parameter
            'available' => array_keys($availableParams),
            'priority' => $parameterPriority
        ];
    }

    /**
     * Get odometer value by specific parameter ID
     */
    private static function getOdometerValueByParameter($data, $parameterId)
    {
        if (!$parameterId || !isset($data[$parameterId])) {
            return 0;
        }

        $value = (int)$data[$parameterId];
        return $value > 0 ? $value : 0;
    }

    /**
     * Get fuel value by specific parameter ID
     */
    private static function getFuelValueByParameter($data, $parameterId)
    {
        if (!$parameterId || !isset($data[$parameterId])) {
            return 0;
        }

        $value = (float)$data[$parameterId];
        return $value > 0 ? $value : 0;
    }

    /**
     * Determine if a brief state change should be processed or ignored
     * This helps prevent false state changes from creating duplicate trips/stops
     */
    private static function shouldProcessBriefStateChange($currentState, $newState, $durationMinutes, $currentLocation, $newData)
    {
        // Always process if the state has been active for minimum duration
        if ($durationMinutes >= self::MIN_STATE_DURATION_MINUTES) {
            return true;
        }

        // For very brief states, check if there's significant movement
        if ($currentLocation && isset($newData['latitude']) && isset($newData['longitude'])) {
            $distance = self::calculateGpsDistance(
                $currentLocation['latitude'],
                $currentLocation['longitude'],
                $newData['latitude'],
                $newData['longitude']
            );

            // If there's significant movement, process the state change
            if ($distance > self::MAX_CONSOLIDATION_DISTANCE_KM) {
                return true;
            }
        }

        // For brief states without significant movement, ignore the state change
        return false;
    }

    /**
     * Create proper sequence by merging all events chronologically and ensuring alternating pattern
     * This is the main fix for the sequence consistency issue
     */
    private static function createProperSequence($stops, $trips)
    {
        // Combine all events with their types and sort by start time
        $allEvents = [];

        // Add all stops
        foreach ($stops as $stop) {
            $allEvents[] = [
                'type' => 'stop',
                'data' => $stop,
                'start_time' => $stop['start_time'],
                'end_time' => $stop['end_time'] === 'Ongoing' ? now() : $stop['end_time']
            ];
        }

        // Add all trips
        foreach ($trips as $trip) {
            $allEvents[] = [
                'type' => 'trip',
                'data' => $trip,
                'start_time' => $trip['start_time'],
                'end_time' => $trip['end_time']
            ];
        }

        // Sort by start time
        usort($allEvents, function ($a, $b) {
            return $a['start_time']->timestamp <=> $b['start_time']->timestamp;
        });

        // Now merge consecutive events of the same type
        $mergedEvents = self::mergeConsecutiveSameTypeEvents($allEvents);

        // Separate back into stops and trips
        $finalStops = [];
        $finalTrips = [];
        $sequence = [];

        foreach ($mergedEvents as $event) {
            if ($event['type'] === 'stop') {
                $finalStops[] = $event['data'];
                $sequence[] = [
                    'type' => 'stop',
                    'start_time' => $event['data']['start_time'],
                    'end_time' => $event['data']['end_time'],
                    'duration' => $event['data']['duration'],
                    'location' => $event['data']['location']
                ];
            } else {
                $finalTrips[] = $event['data'];
                $sequence[] = [
                    'type' => 'trip',
                    'start_time' => $event['data']['start_time'],
                    'end_time' => $event['data']['end_time'],
                    'duration' => $event['data']['duration'],
                    'distance' => $event['data']['distance'],
                    'start_location' => $event['data']['start_location'],
                    'end_location' => $event['data']['end_location']
                ];
            }
        }

        return [
            'stops' => $finalStops,
            'trips' => $finalTrips,
            'sequence' => $sequence
        ];
    }

    /**
     * Merge consecutive events of the same type to ensure proper sequence
     */
    private static function mergeConsecutiveSameTypeEvents($events)
    {
        if (count($events) <= 1) {
            return $events;
        }

        $merged = [];
        $currentEvent = null;

        foreach ($events as $event) {
            if ($currentEvent === null) {
                $currentEvent = $event;
                continue;
            }

            // If same type and should be merged
            if (
                $currentEvent['type'] === $event['type'] &&
                self::shouldMergeConsecutiveEvents($currentEvent, $event)
            ) {

                // Merge the events
                $currentEvent = self::mergeEvents($currentEvent, $event);
            } else {
                // Different type or shouldn't merge - save current and start new
                $merged[] = $currentEvent;
                $currentEvent = $event;
            }
        }

        // Add the last event
        if ($currentEvent !== null) {
            $merged[] = $currentEvent;
        }

        return $merged;
    }

    /**
     * Determine if two consecutive events of the same type should be merged
     */
    private static function shouldMergeConsecutiveEvents($event1, $event2)
    {
        // Calculate time gap between events
        $timeGapMinutes = $event1['end_time']->diffInMinutes($event2['start_time']);

        // Always merge if there's overlap or very small gap
        if ($timeGapMinutes <= self::MAX_CONSOLIDATION_TIME_MINUTES) {
            return true;
        }

        // For same type events that are close in time, check location proximity
        if ($event1['type'] === 'stop' && $event2['type'] === 'stop') {
            $distance = self::calculateGpsDistance(
                $event1['data']['location']['lat'],
                $event1['data']['location']['lng'],
                $event2['data']['location']['lat'],
                $event2['data']['location']['lng']
            );
            return $distance <= self::MAX_CONSOLIDATION_DISTANCE_KM;
        }

        if ($event1['type'] === 'trip' && $event2['type'] === 'trip') {
            $distance = self::calculateGpsDistance(
                $event1['data']['end_location']['lat'],
                $event1['data']['end_location']['lng'],
                $event2['data']['start_location']['lat'],
                $event2['data']['start_location']['lng']
            );
            return $distance <= self::MAX_CONSOLIDATION_DISTANCE_KM;
        }

        return false;
    }

    /**
     * Merge two events of the same type
     */
    private static function mergeEvents($event1, $event2)
    {
        if ($event1['type'] === 'stop') {
            return [
                'type' => 'stop',
                'data' => self::mergeStopData($event1['data'], $event2['data']),
                'start_time' => $event1['start_time'],
                'end_time' => $event2['end_time']
            ];
        } else {
            return [
                'type' => 'trip',
                'data' => self::mergeTripData($event1['data'], $event2['data']),
                'start_time' => $event1['start_time'],
                'end_time' => $event2['end_time']
            ];
        }
    }

    /**
     * Merge two stop data structures
     */
    private static function mergeStopData($stop1, $stop2)
    {
        $endTime2 = $stop2['end_time'] === 'Ongoing' ? 'Ongoing' : $stop2['end_time'];

        // Calculate new duration
        if ($endTime2 !== 'Ongoing') {
            $duration = $stop1['start_time']->diffForHumans($endTime2, true);
            $durationMinutes = $stop1['start_time']->diffInMinutes($endTime2);
        } else {
            $duration = $stop1['duration']; // Keep ongoing
            $durationMinutes = $stop1['duration_minutes'] ?? 0;
        }

        return [
            'time' => $stop1['time'],
            'start_time' => $stop1['start_time'],
            'end_time' => $endTime2,
            'duration' => $duration,
            'duration_minutes' => $durationMinutes,
            'location' => $stop1['location'], // Use first stop's location as primary
            'odometer' => $stop2['odometer'] ?? $stop1['odometer'], // Use latest odometer
            'fuel' => $stop2['fuel'] ?? $stop1['fuel'], // Use latest fuel
            'trip_distance' => ($stop1['trip_distance'] ?? 0) + ($stop2['trip_distance'] ?? 0),
            'trip_fuel' => ($stop1['trip_fuel'] ?? 0) + ($stop2['trip_fuel'] ?? 0)
        ];
    }

    /**
     * Merge two trip data structures
     */
    private static function mergeTripData($trip1, $trip2)
    {
        // Calculate new duration
        $duration = $trip1['start_time']->diffForHumans($trip2['end_time'], true);
        $durationMinutes = $trip1['start_time']->diffInMinutes($trip2['end_time']);

        return [
            'start_time' => $trip1['start_time'],
            'end_time' => $trip2['end_time'],
            'duration' => $duration,
            'duration_minutes' => $durationMinutes,
            'start_location' => $trip1['start_location'], // Use first trip's start location
            'end_location' => $trip2['end_location'], // Use last trip's end location
            'distance' => ($trip1['distance'] ?? 0) + ($trip2['distance'] ?? 0), // Sum distances
            'fuel_consumption' => ($trip1['fuel_consumption'] ?? 0) + ($trip2['fuel_consumption'] ?? 0) // Sum fuel
        ];
    }
}
