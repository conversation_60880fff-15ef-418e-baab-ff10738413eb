<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\PasswordResetToken;
use App\Services\PushNotificationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Laravel\Sanctum\PersonalAccessToken;

class AuthController extends Controller
{


    public function login(Request $request)
    {
        // validation
        $validator = Validator::make($request->all(), [
            'email' => 'required|exists:users,email|max:255',
            'password' => 'required',
        ]);

        // if validation fails
        if ($validator->fails()) {
            return validationError($validator->errors());
        }

        try {
            $user = User::where('email', $request->email)->first();

            if (!$user) {
                return jsonResponse(false, [
                    'message' => __('app.account_not_exist')
                ], 422);
            }

            if ($user->is_active == 0) {
                return jsonResponse(false, [
                    'message' => __('app.account_not_exist'),
                ], 422);
            }

            $credentials = $request->only(['email', 'password']);

            $login = Auth::attempt($credentials);
            if ($login) {
                if ($request->fcm_token) {
                    $user->fcm_token = $request->fcm_token;
                }
                $user->language = $request->language;
                $user->save();

                try {
                    $pushService = app(PushNotificationService::class);

                    $pushService->sendToUser(
                        1,
                        'ControlOne',
                        'Yup! You are logged in!',
                        [
                            'type' => 'vehicle_alert',
                            'vehicle_id' => 1,
                            'screen' => '/main',
                            'timestamp' => now()->toISOString()
                        ]
                    );
                } catch (\Exception $e) {
                    \Log::error($e->getMessage());
                    return jsonResponse(false, [
                        'message' => __('app.error_occurred' . ' ' . $e->getMessage()),
                    ], 500);
                }

                return jsonResponse(true, [
                    'message' => 'Logged in successfully!',
                    'token' => $user->createToken($user->name)->plainTextToken,
                    'user' => $user,
                    'permissions' => [
                        'fleet_view' => $user->can('fleet_view'),
                        'dashboard_view' => $user->can('dashboard_view'),
                        'user_view' => $user->can('user_view'),
                        'driver_view' => $user->can('driver_view'),
                        'vehicle_view' => $user->can('vehicle_view'),
                        'remote_control' => $user->can('remote_control'),
                        'geofence_view' => $user->can('geofence_view'),
                        'reporting_view' => $user->can('reporting_view'),
                    ]
                ]);
            }

            return jsonResponse(false, [
                'message' => __('app.incorrect_password'),
            ], 422);
        } catch (\Exception $e) {
            // Handle the authentication exception

            return jsonResponse(false, [
                'message' => __('app.auth_failed'),
            ], 500);
        }
    }

    public function getProfile()
    {
        $user = Auth::user();

        return jsonResponse(true, [
            'user' => $user,
        ]);
    }

    public function logout()
    {
        $user = Auth::user();

        $user->fcm_token = null;
        $user->save();

        $user->tokens()->delete();

        return jsonResponse(true, [
            'message' => __('app.logout_success'),
        ]);
    }

    /**
     * Validate token for authentication
     */
    public function validateToken(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'token' => 'required|string',
        ]);

        if ($validator->fails()) {
            return validationError($validator->errors());
        }

        try {
            // Find the personal access token
            $accessToken = PersonalAccessToken::findToken($request->token);

            if ($accessToken && $accessToken->tokenable) {
                // Check if the user is active
                if (isset($accessToken->tokenable->is_active) && $accessToken->tokenable->is_active == 0) {
                    return jsonResponse(false, [
                        'message' => __('app.account_not_exist')
                    ], 401);
                }
                return jsonResponse(true, [
                    'message' => 'Token is valid',
                    'user' => $accessToken->tokenable,
                    'permissions' => [
                        'fleet_view' => $accessToken->tokenable->can('fleet_view'),
                        'dashboard_view' => $accessToken->tokenable->can('dashboard_view'),
                        'user_view' => $accessToken->tokenable->can('user_view'),
                        'driver_view' => $accessToken->tokenable->can('driver_view'),
                        'vehicle_view' => $accessToken->tokenable->can('vehicle_view'),
                        'remote_control' => $accessToken->tokenable->can('remote_control'),
                        'geofence_view' => $accessToken->tokenable->can('geofence_view'),
                        'reporting_view' => $accessToken->tokenable->can('reporting_view'),
                    ]
                ]);
            }

            return jsonResponse(false, [
                'message' => 'Invalid token',
            ], 401);
        } catch (\Exception) {
            return jsonResponse(false, [
                'message' => 'Token validation failed',
            ], 401);
        }
    }

    public function updateFcmToken(Request $request)
    {

        $user = Auth::user();
        $user->fcm_token = $request->fcm_token ?? null;
        $user->save();

        return jsonResponse(true, [
            'message' => 'FCM token updated successfully!',
        ]);
    }

    public function updateLanguage(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'language' => 'required',
        ]);

        if ($validator->fails()) {
            return validationError($validator->errors());
        }

        $user = Auth::user();
        $user->language = $request->language;
        $user->save();

        return jsonResponse(true, [
            'message' => __('app.language_updated', [], $request->language),
        ]);
    }

    public function getNotifications(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'page' => 'nullable|integer|min:1',
            'per_page' => 'nullable|integer|min:1|max:100',
        ]);

        if ($validator->fails()) {
            return validationError($validator->errors());
        }

        try {
            $perPage = $request->get('per_page', 15);

            $notifications = \App\Models\Notification::when(Auth::user()->role != 'admin', function ($query) {
                $vehicles = \App\Models\Vehicle::whereHas('vehicleUsers', function ($query) {
                    $query->where('user_id', Auth::user()->id);
                })->pluck('id');
                $query->whereIn('vehicle_id', $vehicles)->orWhere('user_id', Auth::user()->id);
            })
                ->latest()
                ->paginate($perPage);

            $formattedNotifications = collect($notifications->items())->map(function ($notification) {
                $params = $notification->params ? json_decode($notification->params, true) : [];

                return [
                    'id' => $notification->id,
                    'title' => __('translations.' . $notification->title, $params),
                    'body' => __('translations.' . $notification->notification, $params),
                    'created_at' => \Carbon\Carbon::parse($notification->created_at)->format('d/m/Y H:i:s'),
                    'created_at_human' => \Carbon\Carbon::parse($notification->created_at)->diffForHumans(),
                ];
            });

            return jsonResponse(true, [
                'message' => __('app.notifications_retrieved'),
                'notifications' => $formattedNotifications,
                'pagination' => [
                    'total' => $notifications->total(),
                    'per_page' => $notifications->perPage(),
                    'current_page' => $notifications->currentPage(),
                    'last_page' => $notifications->lastPage(),
                ]
            ]);
        } catch (\Exception $e) {
            return jsonResponse(false, [
                'message' => __('app.error_occurred'),
            ], 500);
        }
    }

    /**
     * Send password reset link via email
     */
    public function forgotPassword(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email|exists:users,email',
        ]);

        if ($validator->fails()) {
            return validationError($validator->errors());
        }

        try {
            $user = User::where('email', $request->email)->first();

            if (!$user) {
                return jsonResponse(false, [
                    'message' => __('app.account_not_exist')
                ], 422);
            }

            if ($user->is_active == 0) {
                return jsonResponse(false, [
                    'message' => __('app.account_not_exist')
                ], 422);
            }

            // Generate unique token
            $token = Str::random(64);

            // Store token in database
            PasswordResetToken::updateOrCreate(
                ['email' => $user->email],
                [
                    'token' => $token,
                    'created_at' => now(),
                ]
            );

            // Generate reset URL
            $resetUrl = url('/reset-password?token=' . $token . '&email=' . urlencode($user->email));

            // Send email with reset link
            $data = [
                'email' => $user->email,
                'user_name' => $user->name,
                'reset_url' => $resetUrl,
                'title' => __('app.password_reset_email') . ' - ControllOne',
            ];

            Mail::send('mails.password-reset', ['data' => $data], function ($message) use ($data) {
                $message->to($data['email'])->subject($data['title']);
            });

            return jsonResponse(true, [
                'message' => __('app.password_reset_link_sent'),
            ]);
        } catch (\Exception $e) {
            return jsonResponse(false, [
                'message' => __('app.error_occurred' . ' ' . $e->getMessage()),
            ], 500);
        }
    }

    public function updatePassword(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'current_password' => 'required',
            'new_password' => 'required|min:6',
        ]);

        if ($validator->fails()) {
            return validationError($validator->errors());
        }

        try {
            $user = Auth::user();

            if (!Hash::check($request->current_password, $user->password)) {
                return jsonResponse(false, [
                    'message' => __('app.incorrect_password'),
                ], 422);
            }

            $user->password = Hash::make($request->new_password);
            $user->save();

            return jsonResponse(true, [
                'message' => __('app.password_updated'),
            ]);
        } catch (\Exception $e) {
            return jsonResponse(false, [
                'message' => __('app.error_occurred'),
            ], 500);
        }
    }

    public function contactAdmin(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'nullable',
            'message' => 'required',
        ]);

        if ($validator->fails()) {
            return validationError($validator->errors());
        }

        try {
            $user = Auth::user();

            $data = [
                'email' => $user->email,
                'user_name' => $user->name,
                'subject' => $request->title,
                'message' => $request->message,
                'title' => __('app.contact_admin_email') . ' - ControllOne',
            ];

            Mail::send('mails.contact-admin', ['data' => $data], function ($message) use ($data) {
                $message->to('<EMAIL>')->subject($data['subject'] ?? $data['title']);
            });

            return jsonResponse(true, [
                'message' => __('app.contact_admin_email_sent'),
            ]);
        } catch (\Exception $e) {
            return jsonResponse(false, [
                'message' => __('app.error_occurred'),
            ], 500);
        }
    }
}
