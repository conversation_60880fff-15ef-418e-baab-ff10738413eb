<?php

namespace App\Livewire\Panel;

use App\Models\Geofence;
use App\Models\Vehicle;
use App\Models\VehicleGeofence;
use App\Helpers\TeltonikaCommandHelper;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;
use Livewire\Attributes\On;
use Livewire\Component;

class Geofencing extends Component
{
    public $addEditGeofenceMode = false;
    public $manageVehicleGeofenes = false;
    public $geofenceId = null;
    public $name, $location, $push_notification, $email_notification, $is_active = true, $geofence;

    public $geofences = [];
    public $filter = 'all', $search, $deleteRecordId, $editRecordId;

    public $selectedVehicle;
    public $vehicleSearch;

    public $geofence_in_notification;
    public $geofence_out_notification;


    public function mount()
    {
        $this->fetchGeofences();
    }

    public function addEditGeofence($geofenceId = null)
    {
        $this->addEditGeofenceMode = true;
        $this->geofenceId = $geofenceId;

        if ($geofenceId) {
            $geofence = Geofence::find($geofenceId);
            $this->name = $geofence->name;
            $this->location = $geofence->location;
            $this->geofence = json_decode($geofence->geofence_data, true);
            $this->is_active = $geofence->is_active ? true : false;
            $this->push_notification = $geofence->push_notification ? true : false;
            $this->email_notification = $geofence->email_notification ? true : false;

            // Dispatch event to update map with the geofence in edit mode
            // This will trigger the JavaScript to center the map on the geofence
            $this->dispatch('update-geofences-on-map-edit', geofences: [[
                'id' => $geofence->id,
                'name' => $geofence->name,
                'location' => $geofence->location,
                'is_active' => $geofence->is_active,
                'geofence_data' => json_decode($geofence->geofence_data, true),
            ]]);

            // Dispatch a custom event to notify JavaScript to center the map on this geofence
            $this->dispatch('focus-on-geofence', geofenceData: json_decode($geofence->geofence_data, true));
        } else {
            $this->dispatch('enable-geofence-mode');
        }
        $this->dispatch('enable-suggestions');
    }

    public function backToList()
    {
        $this->addEditGeofenceMode = false;
        $this->manageVehicleGeofenes = false;
        $this->geofenceId = null;

        $this->dispatch('disable-geofence-mode');
        $this->fetchGeofences();
    }

    public function createUpdateGeofence()
    {
        $this->validate([
            'name' => 'required|max:255',
            'location' => 'nullable|max:255',
        ]);

        if (empty($this->geofence)) {
            $this->dispatch('notify',  variant: 'warning', title: 'Error!',  message: 'Please add atleast one geofence!');
            return;
        }

        // Log the geofence data for debugging
        Log::info('Geofence data before save:', ['data' => $this->geofence]);

        if ($this->geofenceId) {
            // Update existing geofence
            $geofence = Geofence::find($this->geofenceId);
            if ($geofence) {
                $geofence->name = $this->name;
                $geofence->location = $this->location;
                $geofence->push_notification = $this->push_notification ? true : false;
                $geofence->email_notification = $this->email_notification ? true : false;
                $geofence->geofence_data = json_encode($this->geofence);
                $geofence->is_active = $this->is_active ? true : false;
                $geofence->save();

                // Update JSON files for all vehicles assigned to this geofence
                $this->updateAllVehiclesForGeofence($geofence->id);
            }
        } else {
            // Create new geofence
            $geofence = Geofence::create([
                'name' => $this->name,
                'location' => $this->location,
                'geofence_data' => json_encode($this->geofence),
                'push_notification' => $this->push_notification ? true : false,
                'email_notification' => $this->email_notification ? true : false,
                'is_active' => $this->is_active ? true : false,
                'user_id' => auth()->id(),
            ]);

            // No need to update vehicles here as a new geofence won't have any vehicles assigned yet
        }

        $this->dispatch('notify',  variant: 'success', title: 'Success!',  message: $this->geofenceId ? 'Geofence updated successfully!' : 'Geofence created successfully!');

        $this->addEditGeofenceMode = false;
        $this->dispatch('disable-geofence-mode');
        $this->reset();
        $this->fetchGeofences();
    }

    public function fetchGeofences()
    {
        $user = auth()->user();
        $query = Geofence::query()->when(!$user->can('all_geofences_access'), function ($query) use ($user) {
            $query->where('user_id', $user->id)
                ->orWhereIn('id', VehicleGeofence::whereIn('vehicle_id', function ($query) {
                    $query->select('vehicle_id')->from('vehicle_users')->where('user_id', auth()->user()->id);
                })->pluck('geofence_id'));
        });

        // Apply search filter
        if ($this->search) {
            $query->where('name', 'like', '%' . $this->search . '%')
                ->orWhere('location', 'like', '%' . $this->search . '%');
        }

        // Apply active/inactive filter
        if ($this->filter === 'active') {
            $query->where('is_active', 1);
        } elseif ($this->filter === 'inactive') {
            $query->where('is_active', 0);
        }

        $this->geofences = $query->latest()->get()->map(function ($geofence) {
            return [
                'id' => $geofence->id,
                'name' => $geofence->name,
                'location' => $geofence->location,
                'is_active' => $geofence->is_active,
                'geofence_data' => json_decode($geofence->geofence_data, true),
            ];
        });

        $this->dispatch('update-geofences-on-map', geofences: $this->geofences);
    }

    public function setFilter($filter)
    {
        $this->filter = $filter;
        $this->fetchGeofences();
    }



    public function updatedSearch($search)
    {
        $this->search = $search;
        $this->fetchGeofences();
    }


    public function deleteRecordConfirmation($deleteRecordId)
    {
        $this->deleteRecordId = $deleteRecordId;
        $this->dispatch('open-modal', name: 'delete-record-modal');
    }

    public function deleteRecord()
    {
        if ($this->deleteRecordId) {

            $vehiclesGeofences = VehicleGeofence::where('geofence_id', $this->deleteRecordId)->get();
            foreach ($vehiclesGeofences as $vehicleGeofence) {
                $this->removeVehicleFromGeofence($vehicleGeofence->id);
                $vehicleGeofence->delete();
            }

            Geofence::find($this->deleteRecordId)->delete();



            $this->dispatch('notify',  variant: 'success', title: 'Success!',  message: 'Geofence deleted successfully');

            $this->dispatch('close-modal');
            $this->reset('deleteRecordId');
            $this->fetchGeofences();
        } else {
            $this->dispatch('notify',  variant: 'danger', title: 'Error!',  message: 'Geofence Not Found');
        }
    }

    public function manageGeofenceVehicles($geofenceId)
    {
        $this->manageVehicleGeofenes = true;
        $this->geofenceId = $geofenceId;

        // First disable geofence drawing mode
        $this->dispatch('disable-geofence-mode');

        // Get the geofence data to display on the map (read-only)
        $geofence = Geofence::find($geofenceId);
        if ($geofence) {
            $this->dispatch('update-geofences-on-map', geofences: [[
                'id' => $geofence->id,
                'name' => $geofence->name,
                'location' => $geofence->location,
                'is_active' => $geofence->is_active,
                'geofence_data' => json_decode($geofence->geofence_data, true),
            ]]);
        }
    }


    #[On('selectedVehicleUpdated')]
    public function selectedVehicleUpdated($vehicleId)
    {
        $this->selectedVehicle = $vehicleId;
    }

    public function editAssignedVehicleGeofence($vehicleGeofenceId)
    {
        $vehicleGeofence = VehicleGeofence::find($vehicleGeofenceId);
        if ($vehicleGeofence) {
            $this->editRecordId = $vehicleGeofence->id;
            $this->selectedVehicle = $vehicleGeofence->vehicle_id;
            $this->push_notification = $vehicleGeofence->push_notification ? true : false;
            $this->email_notification = $vehicleGeofence->email_notification ? true : false;
            $this->geofence_in_notification = $vehicleGeofence->geofence_in_notification ? true : false;
            $this->geofence_out_notification = $vehicleGeofence->geofence_out_notification ? true : false;

            $this->dispatch('valueUpdated', $this->selectedVehicle, 'getVehicles');

            $this->dispatch('open-modal', name: 'add-geofence-vehicle-modal');
        }
    }

    public function assignVehicleToGeofence()
    {
        if ($this->editRecordId) {
            VehicleGeofence::where('id', $this->editRecordId)->update([
                'vehicle_id' => $this->selectedVehicle,
                'push_notification' => $this->push_notification ? true : false,
                'email_notification' => $this->email_notification ? true : false,
                'geofence_in_notification' => $this->geofence_in_notification ? true : false,
                'geofence_out_notification' => $this->geofence_out_notification ? true : false,
            ]);
            $this->dispatch('close-modal');
        } else {
            if ($this->selectedVehicle) {
                $vehicle = Vehicle::find($this->selectedVehicle);
                $geofence = Geofence::find($this->geofenceId);

                if (!$vehicle) {
                    $this->dispatch('notify', variant: 'danger', title: 'Error!', message: 'Vehicle not found!');
                    return;
                }

                // first check if the vehicle is already assigned to the geofence
                if ($geofence->vehicles->contains($this->selectedVehicle)) {
                    $this->dispatch('notify',  variant: 'warning', title: 'Error!',  message: 'Vehicle already assigned to geofence');
                    return;
                }

                // Check if vehicle has IMEI
                if (!$vehicle->imei) {
                    $this->dispatch('notify', variant: 'danger', title: 'Error!', message: 'Vehicle must have an IMEI for device-based geofencing!');
                    return;
                }

                // Check how many geofences are already assigned to this IMEI
                $existingZones = VehicleGeofence::where('vehicle_imei', $vehicle->imei)
                    ->whereNotNull('zone_number')
                    ->count();

                if ($existingZones >= 5) {
                    $this->dispatch('notify', variant: 'danger', title: 'Error!', message: 'Maximum 5 geofences can be assigned to one device!');
                    return;
                }

                // Find next available zone number for this IMEI
                $usedZones = VehicleGeofence::where('vehicle_imei', $vehicle->imei)
                    ->whereNotNull('zone_number')
                    ->pluck('zone_number')
                    ->toArray();

                $nextZone = 1;
                for ($i = 1; $i <= 5; $i++) {
                    if (!in_array($i, $usedZones)) {
                        $nextZone = $i;
                        break;
                    }
                }

                // Create the assignment with zone number
                VehicleGeofence::create([
                    'vehicle_id' => $this->selectedVehicle,
                    'geofence_id' => $this->geofenceId,
                    'vehicle_imei' => $vehicle->imei,
                    'zone_number' => $nextZone,
                    'push_notification' => $this->push_notification ? true : false,
                    'email_notification' => $this->email_notification ? true : false,
                    'geofence_in_notification' => $this->geofence_in_notification ? true : false,
                    'geofence_out_notification' => $this->geofence_out_notification ? true : false,
                ]);

                // Generate and queue device command
                if ($geofence) {
                    $command = TeltonikaCommandHelper::generateGeofenceCommand($nextZone, $geofence);
                    TeltonikaCommandHelper::addCommandToQueue($vehicle->imei, $command);
                }

                $this->updateVehicleGeofences($this->selectedVehicle);

                $this->dispatch('close-modal');
                $this->dispatch('notify',  variant: 'success', title: 'Success!',  message: "Vehicle assigned to geofence successfully! Zone: {$nextZone}");
            } else {
                $this->dispatch('notify',  variant: 'danger', title: 'Error!',  message: 'Please select a vehicle to assign');
            }
        }
    }

    public function removeVehicleFromGeofence($vehicleGeofenceId)
    {
        $vehicleGeofence = VehicleGeofence::find($vehicleGeofenceId);

        if (!$vehicleGeofence) {
            $this->dispatch('notify', variant: 'danger', title: 'Error!', message: 'Assignment not found!');
            return;
        }

        $vehicleId = $vehicleGeofence->vehicle_id;
        $zoneNumber = $vehicleGeofence->zone_number;
        $imei = $vehicleGeofence->vehicle_imei;

        // Delete the assignment
        $vehicleGeofence->delete();

        // Generate command to clear the zone on device
        if ($imei && $zoneNumber) {
            $command = TeltonikaCommandHelper::generateGeofenceCommand($zoneNumber, null);
            TeltonikaCommandHelper::addCommandToQueue($imei, $command);
        }

        $this->updateVehicleGeofences($vehicleId);

        $this->dispatch('notify',  variant: 'success', title: 'Success!',  message: 'Vehicle removed from geofence successfully');
    }

    /**
     * Sync all geofence commands for a specific vehicle's IMEI
     */
    public function syncVehicleGeofences($vehicleId, $vehicleGeofenceId = null)
    {
        $vehicle = Vehicle::find($vehicleId);

        if (!$vehicle || !$vehicle->imei) {
            $this->dispatch('notify', variant: 'danger', title: 'Error!', message: 'Vehicle or IMEI not found!');
            return;
        }

        // Get geofence assignment for this vehicle
        $assignment = VehicleGeofence::with('geofence')
            ->where('vehicle_id', $vehicleId)
            ->where('id', $vehicleGeofenceId ?? 0)
            ->whereNotNull('zone_number')
            ->first();


        if (!$assignment) {
            $this->dispatch('notify', variant: 'danger', title: 'Error!', message: 'Geofence assignment not found!');
            return;
        }

        // Sync commands using helper
        $success = TeltonikaCommandHelper::syncGeofenceCommands($vehicle->imei, $assignment);

        if ($success) {
            $this->dispatch('notify', variant: 'success', title: 'Success!', message: 'Geofence commands synced successfully!');
        } else {
            $this->dispatch('notify', variant: 'danger', title: 'Error!', message: 'Failed to sync geofence commands!');
        }
    }

    /**
     * Update the geofence JSON files for all vehicles assigned to a specific geofence
     *
     * @param int $geofenceId The ID of the geofence that was updated
     * @return void
     */
    private function updateAllVehiclesForGeofence($geofenceId)
    {
        // Get all vehicles assigned to this geofence
        $vehicleGeofences = VehicleGeofence::where('geofence_id', $geofenceId)->get();

        // Update the JSON file for each vehicle
        foreach ($vehicleGeofences as $vehicleGeofence) {
            $this->updateVehicleGeofences($vehicleGeofence->vehicle_id);
        }
    }

    /**
     * Update the geofence JSON file for a specific vehicle
     *
     * @param int $vehicleId The ID of the vehicle
     * @return bool Success status
     */
    private function updateVehicleGeofences($vehicleId)
    {
        // Fetch vehicle with assigned geofences
        $vehicle = Vehicle::with(['geofences' => function ($query) {
            $query->where('is_active', 1); // Fetch only active geofences
        }])->find($vehicleId);

        if (!$vehicle) {
            Log::warning('Vehicle not found when updating geofence JSON', ['vehicle_id' => $vehicleId]);
            return false; // Vehicle not found
        }

        // Extract IMEI from vehicle
        $imei = $vehicle->imei;

        if (!$imei) {
            Log::warning('Vehicle has no IMEI when updating geofence JSON', ['vehicle_id' => $vehicleId]);
            return false; // IMEI is required
        }

        // Prepare geofence data
        $geofenceData = $vehicle->geofences->map(function ($geofence) {
            return [
                'id' => $geofence->id,
                'geofence' => json_decode($geofence->geofence_data, true), // JSON field in DB
            ];
        })->toArray();

        // Define file path
        $directory = public_path('geofences');
        $filePath = $directory . "/{$imei}.json";

        // Ensure directory exists
        if (!File::exists($directory)) {
            File::makeDirectory($directory, 0755, true, true);
        }

        // Save data to JSON file
        File::put($filePath, json_encode($geofenceData, JSON_PRETTY_PRINT));

        return true;
    }

    public function render()
    {
        $vehicles = [];

        if ($this->manageVehicleGeofenes) {
            $geofence = Geofence::find($this->geofenceId);
            if ($geofence) {
                if ($this->vehicleSearch) {
                    $vehicles = VehicleGeofence::with('vehicle')->where('geofence_id', $this->geofenceId)
                        ->whereHas('vehicle', function ($query) {
                            $query->where('license_plate', 'like', '%' . $this->vehicleSearch . '%')
                                ->orWhere('imei', 'like', '%' . $this->vehicleSearch . '%');
                        })
                        ->get();
                } else {
                    $vehicles = VehicleGeofence::with('vehicle')->where('geofence_id', $this->geofenceId)->get();
                }
                // Use update-geofences-on-map instead of update-geofences-on-map-edit to prevent editing
                $this->dispatch('update-geofences-on-map', geofences: [[
                    'id' => $geofence->id,
                    'name' => $geofence->name,
                    'location' => $geofence->location,
                    'is_active' => $geofence->is_active,
                    'geofence_data' => json_decode($geofence->geofence_data, true),
                ]]);
            }
        }
        return view('livewire.panel.geofencing', compact('vehicles'));
    }
}
