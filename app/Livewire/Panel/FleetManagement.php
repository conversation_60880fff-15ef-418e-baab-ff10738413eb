<?php

namespace App\Livewire\Panel;

use App\Models\Driver;
use App\Models\Vehicle;
use App\Models\VehicleGeofence;
use App\Models\VehicleRouteAssignment;
use App\Models\VehicleUser;
use Carbon\Carbon;
use Livewire\Attributes\On;
use Livewire\Component;

class FleetManagement extends Component
{
    public $deviceData;
    public $devices = [];
    public $filter = 'all';
    public $search = null;
    public $vehicleDetails = null;
    public $user;
    private $cachedVehicles = null;
    private $lastDbFetch = null;
    private const DB_CACHE_DURATION = 300; // 5 minutes in seconds

    private const DAILY_ODOMETER_CACHE_KEY = 'previous_day_odometer_';
    private const CACHE_DURATION = 86400 * 7; // 7 days in seconds

    private const HISTORY_CACHE_DURATION = 604800; // 7 days in seconds
    private const HISTORY_CACHE_KEY = 'vehicle_history_';

    public $liveMode = true;
    public $dates = [];
    public $selectedDate;
    public $lastHistoryData = [];
    public $showStops = false;

    public function mount()
    {
        $this->user = auth()->user();
        $this->loadLiveData();
        $this->loadVehiclesFromDb();
    }

    public function setFilter($filter)
    {
        $this->filter = $filter;
    }

    private function loadVehiclesFromDb()
    {
        // Only fetch from DB if cache is expired or empty
        if (
            $this->lastDbFetch === null ||
            (time() - $this->lastDbFetch) > self::DB_CACHE_DURATION ||
            $this->cachedVehicles === null
        ) {

            $this->cachedVehicles = Vehicle::select(
                'id',
                'imei',
                'model',
                'license_plate',
                'type',
                'icon',
                'mileage',
                'current_odometer_reading',
                'driver_id',
                'status'
            )
                ->with(['driver:id,name,user_id'])
                ->where('status', 1)
                ->when(!$this->user->can('all_vehicles_access'), function ($query) {
                    $query->whereIn('id', VehicleUser::where('user_id', $this->user->id)->pluck('vehicle_id'));
                })
                ->get();

            $this->lastDbFetch = time();
        }

        return $this->cachedVehicles;
    }

    public function loadLiveData()
    {
        if ($this->liveMode) {
            $deviceDataPath = public_path('data/live.json');
            try {
                $deviceData = @file_get_contents($deviceDataPath);
                $this->deviceData = json_decode($deviceData, true) ?? [];
            } catch (\Exception $e) {
                \Log::error('Failed to read live data: ' . $e->getMessage());
                $this->deviceData = [];
            }
        }
    }


    private function enrichVehicleWithLiveData($vehicle)
    {
        $imei = $vehicle->imei;
        $deviceData = $this->deviceData[$imei] ?? null;

        if (!$this->liveMode && isset($this->vehicleDetails['imei']) && $this->vehicleDetails['imei'] === $imei && !empty($this->lastHistoryData)) {
            $deviceData = $this->lastHistoryData;
        }

        if (!$deviceData) return $vehicle;

        $vehicle->latitude = $deviceData['latitude'] ?? null;
        $vehicle->longitude = $deviceData['longitude'] ?? null;
        $vehicle->movement_status = $deviceData['240'] ?? 0;
        $vehicle->ignition_status = $deviceData['239'] ?? 0;


        $vehicle->speed = getSpeed($deviceData);

        // Add indicator data
        $vehicle->indicators = [
            'ignition' => [
                'value' => $deviceData['239'] ?? null,
                'available' => isset($deviceData['239'])
            ],
            'parked' => [
                'movement' => $deviceData['240'] ?? null,
                'speed' => getSpeed($deviceData),
                'available' => isset($deviceData['240'])
            ],
            'battery' => [
                'voltage' => $deviceData['67'] ?? null,
                'current' => $deviceData['68'] ?? null,
                'available' => isset($deviceData['67']) || isset($deviceData['68'])
            ],
            'signal' => [
                'value' => $deviceData['21'] ?? null,
                'available' => isset($deviceData['21'])
            ]
        ];
        $vehicle->timestamp = $deviceData['last_update'] ?? null;
        $vehicle->angle = $deviceData['angle'] ?? 0;

        // odometer
        // if (isset($deviceData['192'])) {
        //     $vehicle->odometer = isset($deviceData['192']) ? (round((int) $deviceData['192'] / 1000)) : null;
        // }

        $vehicle->odometer = round(getOdometerValue($deviceData) / 1000);

        if ($vehicle->current_odometer_reading) {
            $vehicle->odometer = $vehicle->odometer + $vehicle->current_odometer_reading;
        }

        // fuel consumption
        $vehicle->fuel_used = getFuelConsumption($deviceData);

        $vehicle->average_fuel = number_format(getAverageFuelValue($vehicle->fuel_used, $vehicle->odometer, 'l_per_100km'), 2);

        // fuel level percentage
        $vehicle->fuel_level_percentage = getFuelLevelPercentage($deviceData);

        // fuel level in leters
        $vehicle->fuel_level = getFuelLevelLiters($deviceData);

        // remaining distance vehicle can travel
        if (isset($deviceData['866'])) {
            $vehicle->remaining_distance = $deviceData['866'];
        }

        if (isset($deviceData['78']) && $deviceData['78'] != 0 && !preg_match('/^0+$/', $deviceData['78'])) {
            $vehicle->ibutton = $deviceData['78'] ?? null;
            $currentDriver = Driver::select('id', 'name')->where('ibutton_code', $vehicle->ibutton)->first();
            $vehicle->driver = $currentDriver;
        }

        return $vehicle;
    }

    #[On('refresh-live-data')]
    public function render()
    {
        $this->loadLiveData();

        $vehicles = $this->loadVehiclesFromDb()
            ->filter(function ($vehicle) {
                return in_array($vehicle->imei, array_keys($this->deviceData ?? []));
            })
            ->when($this->search, function ($collection) {
                return $collection->filter(function ($vehicle) {
                    return $this->matchesSearch($vehicle);
                });
            })
            ->map(function ($vehicle) {
                return $this->enrichVehicleWithLiveData($vehicle);
            });

        // Update selected vehicle details if exists
        if (isset($this->vehicleDetails['imei'])) {
            $selectedVehicle = $vehicles->first(function ($vehicle) {
                return $vehicle->imei === $this->vehicleDetails['imei'];
            });

            if ($selectedVehicle) {
                // Update vehicle details with latest data
                $this->vehicleDetails = array_merge($this->vehicleDetails, $selectedVehicle->toArray());
                $dailyDistance = $this->calculateDailyDistance($this->vehicleDetails);
                $this->vehicleDetails['daily_distance'] = $dailyDistance['distance'] ?? 0;

                $this->vehicleDetails['daily_fuel'] = $dailyDistance['fuel'] ?? 0;


                // Dispatch events with updated data
                $this->dispatch('show-vehicle-details', $this->vehicleDetails);
                $this->dispatch('speed-updated', $this->vehicleDetails['speed'] ?? 0);
                $this->dispatch('fuel-level-updated', $this->vehicleDetails['fuel_level_percentage'] ?? 0);
            }
        }

        // Group and filter devices
        $this->devices = $vehicles->groupBy('type')
            ->map(function ($groupVehicles) {
                return $groupVehicles->filter(function ($vehicle) {
                    return $this->matchesFilter($vehicle);
                });
            })
            ->all();

        $this->dispatch('updateMapData', data: $this->devices);

        return view('livewire.panel.fleet-management');
    }

    private function matchesSearch($vehicle)
    {
        $searchTerm = strtolower($this->search);
        return str_contains(strtolower($vehicle->license_plate), $searchTerm) ||
            str_contains(strtolower($vehicle->imei), $searchTerm) ||
            str_contains(strtolower($vehicle->model), $searchTerm) ||
            str_contains(strtolower($vehicle->type), $searchTerm) ||
            str_contains(strtolower($vehicle->driver->name ?? ''), $searchTerm);
    }

    private function matchesFilter($vehicle)
    {
        if ($this->filter === 'all') return true;

        $movingStatus = $vehicle->movement_status;
        $ignitionStatus = $vehicle->ignition_status;
        $speed = $vehicle->speed;

        return match ($this->filter) {
            'moving' => $movingStatus == 1 && $speed > 0,
            'parked' => $movingStatus == 0 && $ignitionStatus == 0,
            'stopped' => $movingStatus == 0 && $ignitionStatus == 1,
            default => true,
        };
    }

    /**
     * Check if a point is inside a geofence
     *
     * @param array $point ['lat' => float, 'lng' => float]
     * @param array $geofence Geofence data
     * @return bool
     */
    private function isPointInGeofence($point, $geofence)
    {
        if (!isset($point['lat']) || !isset($point['lng']) || !$geofence) {
            return false;
        }

        $geofenceData = $geofence['geofence_data'];
        $type = $geofenceData['type'] ?? null;

        if ($type === 'circle') {
            // Circle geofence
            $center = $geofenceData['geofence'] ?? null;
            $radius = $geofenceData['radius'] ?? 0;

            if (!$center || !isset($center['lat']) || !isset($center['lng'])) {
                return false;
            }

            // Calculate distance between point and center
            $earthRadius = 6371000; // meters
            $lat1 = deg2rad($center['lat']);
            $lng1 = deg2rad($center['lng']);
            $lat2 = deg2rad($point['lat']);
            $lng2 = deg2rad($point['lng']);

            $dlat = $lat2 - $lat1;
            $dlng = $lng2 - $lng1;

            $a = sin($dlat / 2) * sin($dlat / 2) + cos($lat1) * cos($lat2) * sin($dlng / 2) * sin($dlng / 2);
            $c = 2 * atan2(sqrt($a), sqrt(1 - $a));
            $distance = $earthRadius * $c;

            return $distance <= $radius;
        } elseif ($type === 'polygon') {
            // Polygon geofence
            $vertices = $geofenceData['geofence'] ?? [];
            if (empty($vertices)) {
                return false;
            }

            // Ray casting algorithm
            $inside = false;
            $j = count($vertices) - 1;

            for ($i = 0; $i < count($vertices); $i++) {
                $xi = $vertices[$i]['lng'] ?? 0;
                $yi = $vertices[$i]['lat'] ?? 0;
                $xj = $vertices[$j]['lng'] ?? 0;
                $yj = $vertices[$j]['lat'] ?? 0;

                $intersect = (($yi > $point['lat']) != ($yj > $point['lat'])) &&
                    ($point['lng'] < ($xj - $xi) * ($point['lat'] - $yi) / ($yj - $yi) + $xi);

                if ($intersect) {
                    $inside = !$inside;
                }

                $j = $i;
            }

            return $inside;
        } elseif ($type === 'rectangle') {
            // Rectangle geofence
            $bounds = $geofenceData['geofence'] ?? null;

            if (
                !$bounds || !isset($bounds['north']) || !isset($bounds['south']) ||
                !isset($bounds['east']) || !isset($bounds['west'])
            ) {
                return false;
            }

            return $point['lat'] <= $bounds['north'] &&
                $point['lat'] >= $bounds['south'] &&
                $point['lng'] <= $bounds['east'] &&
                $point['lng'] >= $bounds['west'];
        }

        return false;
    }

    public function updateVehicleDetails($vehicle = null)
    {
        $this->liveMode = true;
        $this->selectedDate = null;
        $this->dates = [];

        $this->dispatch('update-mode', liveMode: true, data: []);


        $this->vehicleDetails = $vehicle;
        if ($vehicle) {
            // Existing geofences code...
            $geofences = VehicleGeofence::where('vehicle_id', $vehicle['id'])
                ->with(['geofence' => function ($query) {
                    $query->where('is_active', 1)
                        ->select('id', 'name', 'location', 'geofence_data');
                }])
                ->get()
                ->map(function ($vg) {
                    return [
                        'id' => $vg->geofence->id,
                        'name' => $vg->geofence->name,
                        'location' => $vg->geofence->location,
                        'geofence_data' => json_decode($vg->geofence->geofence_data, true),
                    ];
                })
                ->toArray();

            // Calculate stops
            $stops = $this->calculateTodayStops($vehicle['imei']);


            // Add stops to vehicle details
            $this->vehicleDetails['stops'] = $stops;

            if ($this->showStops) {
                // update stops addresses
                foreach ($this->vehicleDetails['stops'] as $key => $stop) {
                    if (!$stop['location']['address']) {
                        $this->vehicleDetails['stops'][$key]['location']['address'] = getAddressFromCoordinates($stop['location']['lat'], $stop['location']['lng']);
                    }
                }

                // reindex stops
                $this->vehicleDetails['stops'] = array_values($this->vehicleDetails['stops']);

                // update it in cache
                $cacheKey = 'today_stops_' . now()->format('d-m-Y') . $this->vehicleDetails['imei'];
                cache()->put($cacheKey, $this->vehicleDetails['stops'], 300);
            }

            $this->vehicleDetails['total_stops'] = count($stops);
            $this->vehicleDetails['total_stop_time'] = collect($stops)->sum(function ($stop) {
                return parseFlexibleTimestamp($stop['start_time'])
                    ->diffInMinutes($stop['end_time'] === 'Ongoing'
                        ? now()
                        : parseFlexibleTimestamp($stop['end_time']));
            });

            $this->vehicleDetails['current_route'] = $this->getCurrentRoute($vehicle['id']);

            // Check geofence status
            $this->vehicleDetails['geofence_status'] = [];
            if (!empty($geofences) && isset($vehicle['latitude']) && isset($vehicle['longitude'])) {
                $point = ['lat' => (float)$vehicle['latitude'], 'lng' => (float)$vehicle['longitude']];

                foreach ($geofences as $geofence) {
                    $isInside = $this->isPointInGeofence($point, $geofence);
                    $this->vehicleDetails['geofence_status'][] = [
                        'id' => $geofence['id'],
                        'name' => $geofence['name'],
                        'inside' => $isInside
                    ];
                }
            }

            // Add indicator data
            $this->vehicleDetails['indicators'] = [
                'ignition' => [
                    'value' => $this->deviceData[$vehicle['imei']]['239'] ?? null,
                    'available' => isset($this->deviceData[$vehicle['imei']]['239'])
                ],
                'parked' => [
                    'movement' => $this->deviceData[$vehicle['imei']]['240'] ?? null,
                    'speed' => getSpeed($this->deviceData[$vehicle['imei']]),
                    'available' => isset($this->deviceData[$vehicle['imei']]['240'])
                ],
                'battery' => [
                    'voltage' => $this->deviceData[$vehicle['imei']]['67'] ?? null,
                    'current' => $this->deviceData[$vehicle['imei']]['68'] ?? null,
                    'available' => isset($this->deviceData[$vehicle['imei']]['67']) || isset($this->deviceData[$vehicle['imei']]['68'])
                ],
                'signal' => [
                    'value' => $this->deviceData[$vehicle['imei']]['21'] ?? null,
                    'available' => isset($this->deviceData[$vehicle['imei']]['21'])
                ]
            ];

            $this->dispatch('show-vehicle-details', $this->vehicleDetails);
            $this->dispatch('update-geofences-on-map', geofences: $geofences);
            $this->dispatch('fuel-level-updated', $vehicle['fuel_level_percentage'] ?? 0);
            $this->dispatch('show-vehicle-route', route: $this->vehicleDetails['current_route'] ?? null);
        }
    }


    private function getCurrentRoute($vehicleId, $date = null)
    {
        if ($date) {
            $date = Carbon::createFromFormat('d-m-Y', $date)->format('Y-m-d') ?? now()->format('Y-m-d');
        }
        $vehicleRoute = VehicleRouteAssignment::where('vehicle_id', $vehicleId)
            ->whereDate('start_date', $date ?? now()->toDateString())
            ->with('stops')
            ->first();

        if ($vehicleRoute) {
            // Convert stops to array
            $vehicleRoute->stops = $vehicleRoute->stops?->toArray();
            return $vehicleRoute->toArray();
        } else {
            return null;
        }
    }


    private function calculateDailyDistance($vehicle)
    {
        $imei = $vehicle['imei'];
        $currentOdometer = $vehicle['odometer'] ?? 0;
        $currentFuel = $vehicle['fuel_used'] ?? 0;
        $initialReading = $vehicle['current_odometer_reading'] ?? 0;

        // Get previous day's last readings
        $previousDayReadings = $this->getPreviousDayReadings($imei);

        if ($previousDayReadings === null) {
            return [
                'distance' => 0,
                'fuel' => 0
            ];
        }

        // Calculate actual distances (including initial reading only once)
        $currentTotal = $currentOdometer;

        // Convert values to float and handle potential null/invalid values
        $previousDayOdometer = is_numeric($previousDayReadings['odometer']) ? (float)$previousDayReadings['odometer'] : 0;
        $previousDayFuel = is_numeric($previousDayReadings['fuel']) ? (float)$previousDayReadings['fuel'] : 0;
        $initialReading = is_numeric($initialReading) ? (float)$initialReading : 0;

        $previousTotal = $previousDayOdometer + $initialReading;

        return [
            'distance' => $currentTotal > $previousTotal ? $currentTotal - $previousTotal : 0,
            'fuel' => round(max(0, abs($currentFuel - $previousDayFuel)), 2)
        ];
    }

    private function getPreviousDayReadings($imei)
    {
        $yesterday = now()->subDay()->format('d-m-Y');

        if ($this->selectedDate) {
            $yesterday = Carbon::createFromFormat('d-m-Y', $this->selectedDate)->subDay()->format('d-m-Y');
        }

        $cacheKey = self::DAILY_ODOMETER_CACHE_KEY . $yesterday . $imei;

        // Try to get from cache first
        $cachedValue = cache()->get($cacheKey);
        if ($cachedValue !== null) {
            return $cachedValue;
        }

        // If not in cache, fetch from file
        $previousDayReadings = $this->fetchPreviousDayReadings($imei);


        // Cache the value if found (but not if null)
        if ($previousDayReadings !== null) {
            cache()->put($cacheKey, $previousDayReadings, self::CACHE_DURATION);
        }

        return $previousDayReadings;
    }

    private function fetchPreviousDayReadings($imei)
    {
        try {
            $yesterday = now()->subDay()->format('d-m-Y');
            if ($this->selectedDate) {
                $yesterday = Carbon::createFromFormat('d-m-Y', $this->selectedDate)->subDay()->format('d-m-Y');
            }
            $filePath = public_path("data/history/{$imei}/{$yesterday}.json");

            if (!file_exists($filePath)) {
                return null;
            }

            $historyData = json_decode(@file_get_contents($filePath), true);

            if (empty($historyData)) {
                return null;
            }

            // Get odometer value
            $odometer = $this->findOdometerValueRecursively($historyData);
            $odometer = round($odometer / 1000, 2);

            // Get fuel value by searching recursively through previous records
            $fuel = $this->findFuelValueRecursively($historyData);

            return [
                'odometer' => $odometer,
                'fuel' => $fuel
            ];
        } catch (\Exception $e) {
            \Log::error("Error fetching previous day readings for IMEI {$imei}: " . $e->getMessage());
            return null;
        }
    }

    private function findFuelValueRecursively($historyData)
    {
        // Try each fuel parameter ID separately to maintain consistency
        $fuelParameterIds = getFuelConsumptionParameterIds();

        foreach ($fuelParameterIds as $parameterId) {
            // Start from the last record and move backwards for this specific parameter
            $totalRecords = count($historyData);
            for ($i = $totalRecords - 1; $i >= 0; $i--) {
                $entry = $historyData[$i];
                $fuelValue = getFuelConsumptionByParameter($entry, $parameterId);

                // If we found a non-zero fuel value for this parameter, return it
                if ($fuelValue > 0) {
                    return $fuelValue;
                }
            }
        }

        // If no fuel value found in any record for any parameter
        return 0;
    }

    private function findOdometerValueRecursively($historyData)
    {
        // Try each odometer parameter ID separately to maintain consistency
        $odometerParameterIds = getOdometerParameterIds();

        foreach ($odometerParameterIds as $parameterId) {
            // Start from the last record and move backwards for this specific parameter
            $totalRecords = count($historyData);
            for ($i = $totalRecords - 1; $i >= 0; $i--) {
                $entry = $historyData[$i];
                $odometerValue = getOdometerValueByParameter($entry, $parameterId);

                // If we found a non-zero odometer value for this parameter, return it
                if ($odometerValue > 0) {
                    return $odometerValue;
                }
            }
        }

        // If no odometer value found in any record for any parameter
        return 0;
    }



    // Add this method to manually refresh the cache if needed
    public function refreshOdometerCache($imei)
    {
        cache()->forget(self::DAILY_ODOMETER_CACHE_KEY . now()->subDay()->format('d-m-Y') . $imei);
        $this->getPreviousDayOdometer($imei); // This will fetch and cache new value
    }

    private function calculateStopDuration($timestamp1, $timestamp2)
    {
        $start = parseFlexibleTimestamp($timestamp1);
        $end = parseFlexibleTimestamp($timestamp2);

        $diffInMinutes = abs($end->diffInMinutes($start));

        if ($diffInMinutes < 60) {
            return round($diffInMinutes) . 'm';
        } elseif ($diffInMinutes < 1440) { // less than 24 hours
            $hours = floor($diffInMinutes / 60);
            $minutes = $diffInMinutes % 60;
            return round($hours) . 'h' . ($minutes > 0 ? ' ' . round($minutes) . 'm' : '');
        } else {
            $days = floor($diffInMinutes / 1440);
            return round($days) . 'd';
        }
    }

    private function calculateTodayStops($imei, $date = null)
    {
        $today = $date ?? now()->format('d-m-Y');
        $cacheKey = 'today_stops_' . $today . $imei;

        // Try to get from cache first (cache for 5 minutes)
        if ($cachedStops = cache()->get($cacheKey)) {
            return $cachedStops;
        }

        try {
            $filePath = public_path("data/history/{$imei}/{$today}.json");

            if (!file_exists($filePath)) {
                return [];
            }

            $historyData = json_decode(@file_get_contents($filePath), true);
            if (empty($historyData)) {
                return [];
            }

            $stops = [];
            $lastTripStop = null;

            foreach ($historyData as $index => $data) {
                // Skip entries without coordinates
                if (!isset($data['latitude']) || !isset($data['longitude'])) {
                    continue;
                }

                $point = [
                    'lat' => (float)$data['latitude'],
                    'lng' => (float)$data['longitude'],
                    'timestamp' => $data['last_update'] ?? null,
                    'angle' => (float)($data['angle'] ?? 0),
                    'speed' => (float)(getSpeed($data) ?? 0),
                    'ignition' => $data['239'] ?? null, // ignition status
                    'movement' => $data['240'] ?? null, // movement status
                    'trip_event' => $data['eventID'] ?? null,
                    'trip_status' => isset($data['250']) ? (int)$data['250'] : 0, // trip status
                    'trip_odometer' => getOdometerValue($data) ?? 0, // trip odometer
                    'index' => $index
                ];

                // Primary method: Track trip events for stop duration
                if (isset($data['eventID']) && $data['eventID'] === 250) {
                    if ($point['trip_status'] === 0) { // Vehicle stopped
                        if (!$lastTripStop) { // Only set if not already tracking a stop
                            $lastTripStop = $point;
                            $lastTripStop['totalOdometer'] = $point['trip_odometer']; // Store total distance at stop
                        }
                    } elseif ($point['trip_status'] === 1 && $lastTripStop) { // Vehicle started
                        // Calculate stop duration
                        $duration = $this->calculateStopDuration($lastTripStop['timestamp'], $point['timestamp']);
                        $durationInMinutes = $this->getDurationInMinutes($duration);

                        // Only add stops with duration greater than 1 minute
                        if ($durationInMinutes > 1) {
                            $tripDistance = max(0, ($point['trip_odometer'] - $lastTripStop['trip_odometer']));

                            $stops[] = [
                                'start_time' => $lastTripStop['timestamp'],
                                'end_time' => $point['timestamp'],
                                'duration' => $duration,
                                'location' => [
                                    'lat' => $lastTripStop['lat'],
                                    'lng' => $lastTripStop['lng'],
                                    'address' => null
                                ],
                                'trip_distance' => round($tripDistance / 1000, 2), // Convert to km
                                'total_distance' => round(($lastTripStop['totalOdometer'] / 1000), 2) // Match frontend format
                            ];
                        }
                        $lastTripStop = null;
                    }
                }
                // Backup method: Also detect stops based on speed and ignition
                elseif ($point['speed'] < 1 && isset($point['ignition']) && $point['ignition'] == 1) {
                    if (!$lastTripStop) {
                        $lastTripStop = $point;
                        $lastTripStop['totalOdometer'] = $point['trip_odometer'];
                    }
                }
                // Vehicle started moving again
                elseif ($point['speed'] > 5 && $lastTripStop) {
                    $duration = $this->calculateStopDuration($lastTripStop['timestamp'], $point['timestamp']);
                    $durationInMinutes = $this->getDurationInMinutes($duration);

                    // Only add stops with duration greater than 1 minute
                    if ($durationInMinutes > 1) {
                        $tripDistance = max(0, ($point['trip_odometer'] - $lastTripStop['trip_odometer']));

                        $stops[] = [
                            'start_time' => $lastTripStop['timestamp'],
                            'end_time' => $point['timestamp'],
                            'duration' => $duration,
                            'location' => [
                                'lat' => $lastTripStop['lat'],
                                'lng' => $lastTripStop['lng'],
                                'address' => null
                            ],
                            'trip_distance' => round($tripDistance / 1000, 2), // Convert to km
                            'total_distance' => round(($lastTripStop['totalOdometer'] / 1000), 2) // Match frontend format
                        ];
                    }
                    $lastTripStop = null;
                }
            }

            // Handle ongoing stop
            if ($lastTripStop) {
                $duration = $this->calculateStopDuration($lastTripStop['timestamp'], now()->format('d/m/Y H:i:s'));
                $durationInMinutes = $this->getDurationInMinutes($duration);

                // Only add ongoing stop if duration is greater than 1 minute
                if ($durationInMinutes > 1) {
                    $lastData = end($historyData);
                    $currentOdometer = getOdometerValue($lastData) ?? 0;
                    $tripDistance = max(0, ($currentOdometer - $lastTripStop['trip_odometer']));

                    $stops[] = [
                        'start_time' => $lastTripStop['timestamp'],
                        'end_time' => Carbon::createFromFormat('d-m-Y', $lastTripStop['timestamp']->format('d-m-Y'))->endOfDay(),
                        'duration' => $duration,
                        'location' => [
                            'lat' => $lastTripStop['lat'],
                            'lng' => $lastTripStop['lng'],
                            'address' => null
                        ],
                        'trip_distance' => round($tripDistance / 1000, 2), // Convert to km
                        'total_distance' => round(($lastTripStop['totalOdometer'] / 1000), 2) // Match frontend format
                    ];
                }
            }

            // Cache the results for 5 minutes
            cache()->put($cacheKey, $stops, 300);

            return $stops;
        } catch (\Exception $e) {
            \Log::error("Error calculating stops for IMEI {$imei}: " . $e->getMessage());
            return [];
        }
    }

    public function updatedShowStops()
    {
        // update stops addresses
        foreach ($this->vehicleDetails['stops'] as $key => $stop) {
            if (!$stop['location']['address']) {
                $this->vehicleDetails['stops'][$key]['location']['address'] = getAddressFromCoordinates($stop['location']['lat'], $stop['location']['lng']);
            }
        }

        // reindex stops
        $this->vehicleDetails['stops'] = array_values($this->vehicleDetails['stops']);


        // update it in cache
        $cacheKey = 'today_stops_' . now()->format('d-m-Y') . $this->vehicleDetails['imei'];
        cache()->put($cacheKey, $this->vehicleDetails['stops'], 300);
    }


    private function getDurationInMinutes($duration)
    {
        if (strpos($duration, 'h') !== false) {
            list($hours, $minutes) = explode('h', $duration);
            return (int) $hours * 60 + (int) trim($minutes, 'm');
        } elseif (strpos($duration, 'm') !== false) {
            return (int) trim($duration, 'm');
        } else {
            return 0;
        }
    }



    // history mode
    public function updatedLiveMode()
    {
        if ($this->liveMode) {
            $this->dates = [];
            $this->selectedDate = null;

            $this->dispatch('update-mode', liveMode: true, data: []);
        } else {
            $this->dates = [];

            $this->getHistoryDates();

            $this->selectedDate = $this->dates[0] ?? null;

            $deviceData = $this->getCachedHistoryData($this->selectedDate);

            $this->processHistoryData($deviceData);
        }
    }

    public function getHistoryDates()
    {
        $imei = $this->vehicleDetails['imei'] ?? null;
        if (!$imei) return;

        $filePath = public_path("data/history/{$imei}/dates.json");

        if (!file_exists($filePath)) {
            $this->dates = [];
            return;
        }

        $dates = json_decode(@file_get_contents($filePath), true);
        $this->dates = $dates ?? [];
    }

    private function getCachedHistoryData($date)
    {
        $imei = $this->vehicleDetails['imei'] ?? null;
        if (!$imei || !$date) return [];

        $cacheKey = self::HISTORY_CACHE_KEY . "data_{$imei}_{$date}";

        $cacheDuration = self::HISTORY_CACHE_DURATION;
        if ($date == now()->format('d-m-Y')) {
            $cacheDuration = 180;
        }

        return cache()->remember($cacheKey, $cacheDuration, function () use ($imei, $date) {
            $filePath = public_path("data/history/{$imei}/{$date}.json");

            if (!file_exists($filePath)) {
                return [];
            }

            $historyData = json_decode(@file_get_contents($filePath), true);
            return !empty($historyData) ? $historyData : [];
        });
    }


    public function getHistoryData($date)
    {
        if (!$date) return;
        $imei = $this->vehicleDetails['imei'] ?? null;
        if (!$imei) return;

        $filePath = public_path("data/history/{$imei}/{$date}.json");
        if (!file_exists($filePath)) return;

        $historyData = json_decode(@file_get_contents($filePath), true);
        if (empty($historyData)) return;

        return $historyData;
    }

    public function processHistoryData($deviceData)
    {
        $this->lastHistoryData = end($deviceData) ?? [];

        $this->dispatch('update-mode', liveMode: false, data: $deviceData);

        $imei = $this->vehicleDetails['imei'] ?? null;

        $cacheDuration = self::HISTORY_CACHE_DURATION;
        if ($this->selectedDate == now()->format('d-m-Y')) {
            $cacheDuration = 180;
        }
        // Cache stops calculation
        $cacheKey = self::HISTORY_CACHE_KEY . "stops_{$imei}_{$this->selectedDate}";
        $stops = cache()->remember($cacheKey, $cacheDuration, function () use ($imei) {
            return $this->calculateTodayStops($imei, $this->selectedDate);
        });


        // Add stops to vehicle details
        $this->vehicleDetails['stops'] = $stops;
        $this->vehicleDetails['total_stops'] = count($stops);
        $this->vehicleDetails['total_stop_time'] = collect($stops)->sum(function ($stop) {
            return parseFlexibleTimestamp($stop['start_time'])
                ->diffInMinutes($stop['end_time'] === 'Ongoing'
                    ? now()
                    : parseFlexibleTimestamp($stop['end_time']));
        });


        // Cache current route
        $cacheKey = self::HISTORY_CACHE_KEY . "route_{$imei}_{$this->selectedDate}";
        $this->vehicleDetails['current_route'] = cache()->remember($cacheKey, $cacheDuration, function () {
            return $this->getCurrentRoute($this->vehicleDetails['id'] ?? null, $this->selectedDate);
        });

        // Add indicator data from last history data point
        if (!empty($this->lastHistoryData)) {
            $this->vehicleDetails['indicators'] = [
                'ignition' => [
                    'value' => $this->lastHistoryData['239'] ?? null,
                    'available' => isset($this->lastHistoryData['239'])
                ],
                'parked' => [
                    'movement' => $this->lastHistoryData['240'] ?? null,
                    'speed' => getSpeed($this->lastHistoryData),
                    'available' => isset($this->lastHistoryData['240'])
                ],
                'battery' => [
                    'voltage' => $this->lastHistoryData['67'] ?? null,
                    'current' => $this->lastHistoryData['68'] ?? null,
                    'available' => isset($this->lastHistoryData['67']) || isset($this->lastHistoryData['68'])
                ],
                'signal' => [
                    'value' => $this->lastHistoryData['21'] ?? null,
                    'available' => isset($this->lastHistoryData['21'])
                ]
            ];

            // Check geofence status for historical data
            $geofences = VehicleGeofence::where('vehicle_id', $this->vehicleDetails['id'])
                ->with(['geofence' => function ($query) {
                    $query->where('is_active', 1)
                        ->select('id', 'name', 'location', 'geofence_data');
                }])
                ->get()
                ->map(function ($vg) {
                    return [
                        'id' => $vg->geofence->id,
                        'name' => $vg->geofence->name,
                        'location' => $vg->geofence->location,
                        'geofence_data' => json_decode($vg->geofence->geofence_data, true),
                    ];
                })
                ->toArray();

            $this->vehicleDetails['geofence_status'] = [];
            if (!empty($geofences) && isset($this->lastHistoryData['latitude']) && isset($this->lastHistoryData['longitude'])) {
                $point = ['lat' => (float)$this->lastHistoryData['latitude'], 'lng' => (float)$this->lastHistoryData['longitude']];

                foreach ($geofences as $geofence) {
                    $isInside = $this->isPointInGeofence($point, $geofence);
                    $this->vehicleDetails['geofence_status'][] = [
                        'id' => $geofence['id'],
                        'name' => $geofence['name'],
                        'inside' => $isInside
                    ];
                }
            }
        }

        $this->dispatch('show-vehicle-details', $this->vehicleDetails);
        // $this->dispatch('fuel-level-updated', $vehicle['fuel_level_percentage'] ?? 0);
        $this->dispatch('show-vehicle-route', route: $this->vehicleDetails['current_route'] ?? null);

        if (!empty($geofences ?? [])) {
            $this->dispatch('update-geofences-on-map', geofences: $geofences);
        }

        // cache the history data for that fetched date
    }

    public function updatedSelectedDate()
    {
        $deviceData = $this->getCachedHistoryData($this->selectedDate);
        $this->processHistoryData($deviceData);
    }
}
