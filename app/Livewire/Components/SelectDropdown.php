<?php

namespace App\Livewire\Components;

use App\Models\Driver;
use App\Models\Geofence;
use App\Models\User;
use App\Models\Vehicle;
use App\Models\VehicleUser;
use Livewire\Attributes\On;
use Livewire\Component;

class SelectDropdown extends Component
{

    public $label;
    public $placeholder;
    public $fieldName;
    public $options = [];
    public $filteredOptions = [];
    public $selectedOption;
    public $search = ''; // For search functionality
    public $open = false;
    public $fetchMethod;

    // Dynamically call the provided fetch method
    public function fetchOptions()
    {
        if ($this->fetchMethod && method_exists($this, $this->fetchMethod)) {
            $this->options = call_user_func([$this, $this->fetchMethod]);
            $this->filteredOptions = $this->options; // Initialize filtered options
            $this->search = ''; // Clear search when opening dropdown
            $this->open = true;
        } else {
            throw new \Exception("Fetch method '{$this->fetchMethod}' not defined or does not exist.");
        }
    }

    #[On('valueUpdated')]
    public function valueUpdated($option = null, $type = null)
    {
        if ($type == $this->fetchMethod) {
            $this->selectedOption = $option;
            $this->fetchOptions();
            $this->open = false;
        }
    }

    // Filter options based on the search input
    public function updatedSearch()
    {
        if (is_array($this->options)) {
            $this->filteredOptions = array_filter($this->options, function ($option) {
                return str_contains(strtolower($option), strtolower($this->search));
            });
        } else {
            $this->filteredOptions = $this->options->filter(function ($option) {
                return str_contains(strtolower($option), strtolower($this->search));
            })->all();
        }
    }

    // Toggle the selected state of an option
    public function toggleOption($option)
    {
        if ($option == $this->selectedOption) {
            $this->selectedOption = null;
        } else {
            $this->selectedOption = $option;
            $this->open = false;
        }

        // Dispatch event to reflect changes
        $this->dispatch($this->fieldName . 'Updated', $this->selectedOption);
    }

    // Clear the selected option
    public function clearSelection()
    {
        $this->selectedOption = null;
        $this->search = '';
        $this->open = false;

        // Dispatch event to reflect changes
        $this->dispatch($this->fieldName . 'Updated', $this->selectedOption);
    }

    // methods to fetch data
    public function getDrivers()
    {
        return Driver::where('status', 1)->when(!auth()->user()->can('all_drivers_access'), function ($query) {
            $query->where('user_id', auth()->id());
        })->latest()->pluck('name', 'id');
    }

    public function getVehicles()
    {
        return Vehicle::where('status', 1)->when(!auth()->user()->can('all_vehicles_access'), function ($query) {
            $query->whereIn('id', VehicleUser::where('user_id', auth()->user()->id)->pluck('vehicle_id'));
        })->latest()->pluck('license_plate', 'id');
    }
    public function getUsers()
    {
        return User::where('is_active', 1)->latest()->pluck('name', 'id');
    }

    public function getGeofences()
    {
        return Geofence::where('is_active', 1)->latest()->pluck('name', 'id');
    }

    public function render()
    {
        return view('livewire.components.select-dropdown');
    }
}
