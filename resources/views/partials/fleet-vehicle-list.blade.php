<div x-data="{ open: true, showModal: false }" x-bind:class="open == true ? 'w-full md:w-[28%]' : 'w-fit'"
    class="absolute top-0 bottom-0 left-0 right-0 md:relative z-[999999999]">
    <!-- Floating Toggle Button -->
    <button @click="open = !open" x-data="{ showTooltip: false }" @mouseenter="showTooltip = true" @mouseleave="showTooltip = false"
        class="absolute z-[9999999999999] block p-2 text-white transition-all duration-300 rounded-full shadow-md md:bottom-5 top-20 md:top-auto bg-primary hover:bg-primaryDark"
        x-bind:class="open ? 'md:left-[100%] left-[90%]' : 'left-0'">
        <svg x-bind:class="open ? 'rotate-0' : 'rotate-180'" xmlns="http://www.w3.org/2000/svg" fill="none"
            viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-6 h-6">
            <path stroke-linecap="round" stroke-linejoin="round" d="M15 19l-7-7 7-7" />
        </svg>

        <div x-show="showTooltip" x-transition:enter="transition ease-out duration-200"
            x-transition:enter-start="opacity-0 transform scale-95"
            x-transition:enter-end="opacity-100 transform scale-100"
            x-transition:leave="transition ease-in duration-150"
            x-transition:leave-start="opacity-100 transform scale-100"
            x-transition:leave-end="opacity-0 transform scale-95"
            class="absolute left-0 px-3 py-1 mb-2 text-xs text-white bg-gray-800 rounded shadow-lg bottom-full w-max"
            style="display: none;">
            @lang('translations.vehicle_list')
        </div>
    </button>
    {{-- sidebar --}}
    <div class="2xl:p-5 p-3 bg-white dark:bg-slate-800 shadow-[5px_0px_5px_rgba(0,0,0,0.1)] overflow-y-scroll h-full w-full"
        x-bind:class="open == true ? 'block fixed md:relative z-[99999999]' : 'hidden'">
        <div class="flex items-center justify-between">
            <h2 class="text-lg font-semibold text-slate-800 dark:text-slate-100">
                @lang('translations.vehicles_list')
            </h2>

            <!-- Add loading states -->
            <div wire:loading wire:target="loadLiveData" class="inset-0 z-50 flex items-center justify-center">
                <div class="w-8 h-8 border-b-2 rounded-full animate-spin border-primary"></div>
            </div>
        </div>

        <label
            class="flex items-center mt-3 text-sm border rounded-sm ps-2 border-slate-300 focus-within:border-slate-400 text-slate-700 dark:text-slate-300 dark:border-slate-500 dark:focus-within:border-slate-400">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                stroke="currentColor" class="size-5">
                <path stroke-linecap="round" stroke-linejoin="round"
                    d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z" />
            </svg>
            <input type="text" wire:model.live.debounce.500ms="search"
                class="px-2 py-2 bg-transparent border-none outline-none" placeholder="Search">
        </label>

        <div class="flex flex-wrap gap-2 mt-3 text-xs">
            <!-- All Tab -->
            <button wire:click="setFilter('all')"
                class="px-3 py-2 text-gray-500 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-300 @if ($filter == 'all') ring-2 ring-red-300 border-primary text-primary @endif">
                @lang('translations.all')
            </button>

            <!-- Moving Tab -->
            <button wire:click="setFilter('moving')"
                class="flex items-center px-3 py-2 space-x-2 text-gray-800 border border-gray-300 rounded-lg dark:text-slate-300 hover:border-green-500 hover:text-green-500 focus:outline-none focus:ring-2 focus:ring-green-300  @if ($filter == 'moving') border-green-300 ring-2 ring-green-300 @endif">
                <span class="bg-green-500 rounded-full size-2"></span>
                <span>@lang('translations.moving')</span>
            </button>

            <!-- Stopped Tab -->
            <button wire:click="setFilter('stopped')"
                class="flex items-center px-3 py-2 space-x-2 text-gray-800 border border-gray-300 rounded-lg dark:text-slate-300 hover:border-yellow-500 hover:text-yellow-500 focus:outline-none focus:ring-2 focus:ring-yellow-300  @if ($filter == 'stopped') ring-2 ring-yellow-300 border-yellow-300 @endif">
                <span class="bg-yellow-500 rounded-full size-2"></span>
                <span>@lang('translations.stopped')</span>
            </button>

            <!-- Parked Tab -->
            <button wire:click="setFilter('parked')"
                class="flex items-center px-3 py-2 space-x-2 text-gray-800 border border-gray-300 rounded-lg dark:text-slate-300 hover:border-red-500 hover:text-red-500 focus:outline-none focus:ring-2 focus:ring-red-300  @if ($filter == 'parked') ring-2 ring-red-300 border-red-300 @endif">
                <span class="bg-red-500 rounded-full size-2"></span>
                <span>@lang('translations.parked')</span>
            </button>
        </div>

        <div class="mt-3 text-gray-500 dark:text-gray-300">
            @lang('translations.vehicles') ({{ array_sum(array_map(fn($vehicles) => count($vehicles), $devices)) }})
        </div>

        <div x-data="{
            vehicleDetails: $wire.entangle('vehicleDetails').live,
            selectedImei: null,
            init() {
                this.$watch('vehicleDetails', (value) => {
                    if (value) {
                        this.selectedImei = value.imei;
                        this.showModal = true;
                    }
                });
            }
        }"
            x-on:update-selected-vehicle.window="
    if ($event.detail.imei === selectedImei) {
        vehicleDetails = $event.detail;
    }
">
            @php
                $index = 0;
            @endphp
            @foreach ($devices as $type => $vehicles)
                <div wire:key="devices-{{ ++$index }}" x-data="{ show: true }">
                    <h3 @click="show =!show"
                        class="flex items-center gap-2 mt-3 text-base font-semibold cursor-pointer select-none dark:text-gray-100">
                        <svg width="12" height="13" class="transition-all duration-300"
                            :class="show ? 'rotate-0' : 'rotate-180'" viewBox="0 0 12 13" fill="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <path d="M6 2.89999L0 9.29999H12L6 2.89999Z" fill="currentColor" />
                        </svg>
                        @lang('translations.' . $type)
                        <img class="size-5" src="{{ asset('assets/images/icons/' . ($type ?? 'default') . '.svg') }}"
                            alt="icon">
                    </h3>
                    <div x-show="show" x-cloak class="mt-3 space-y-2">
                        @foreach ($vehicles as $vehicle)
                            <!-- List Item -->
                            <div wire:key="vehicles-{{ $vehicle->id ?? 0 }}"
                                wire:click="updateVehicleDetails({{ $vehicle }})"
                                @click="if (window.innerWidth < 600) open = false;focusOnMarker('{{ $vehicle->imei }}', {{ $vehicle->latitude }}, {{ $vehicle->longitude }}, '{{ $vehicle->icon }}');"
                                class="flex items-end justify-between p-3 pb-4 transition-all duration-300 border-b border-gray-200 rounded-t-sm cursor-pointer hover:bg-gray-100 dark:hover:bg-slate-900 gap-2 flex-wrap @if (isset($vehicleDetails['imei']) && $vehicleDetails['imei'] == $vehicle->imei) bg-primary/10 border-l-4 border-l-primary @endif">

                                <!-- Left Section -->
                                <div class="flex items-center space-x-2 2xl:space-x-4">
                                    <!-- Status Dot -->
                                    @isset($vehicle->movement_status)
                                        @if ($vehicle->movement_status == 1 && $vehicle->speed > 0)
                                            <span class="bg-green-500 rounded-full size-2"></span>
                                        @elseif ($vehicle->movement_status == 0 && $vehicle->ignition_status == 1)
                                            <span class="bg-yellow-500 rounded-full size-2"></span>
                                        @else
                                            <span class="bg-red-500 rounded-full size-2"></span>
                                        @endif
                                    @endisset
                                    <!-- Content -->
                                    <div>
                                        <p
                                            class="flex items-center gap-2 text-sm font-semibold text-gray-900 2xl:text-base dark:text-gray-200">
                                            {{ $vehicle->license_plate }}
                                        </p>
                                        @if ($vehicle?->driver?->name)
                                            <div
                                                class="flex items-center mt-1 text-gray-600 2xl:space-x-1 dark:text-slate-300">
                                                <span class="inline-flex items-center">
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="size-3 2xl:size-4"
                                                        fill="currentColor" viewBox="0 0 24 24">
                                                        <path
                                                            d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-3.31 0-6 2.69-6 6h12c0-3.31-2.69-6-6-6z">
                                                        </path>
                                                    </svg>
                                                </span>
                                                <span class="text-xs">{{ $vehicle->driver?->name }}</span>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                                <!-- Right Section -->
                                <p
                                    class="2xl:text-[10px] text-[8px] text-gray-500 dark:text-slate-400 text-end w-full shrink-0 lg:w-fit lg:shrink">
                                    {{ isset($vehicle->timestamp) ? parseFlexibleTimestamp($vehicle->timestamp)->diffForHumans() : '' }}
                                </p>
                            </div>
                        @endforeach

                    </div>
                </div>
            @endforeach

            {{-- vehicle detail modal --}}


        </div>

    </div>

    <div x-show="showModal" x-cloak
        class="fixed bottom-0 md:top-0 right-0 z-[9999999] flex items-end overflow-y-scroll left-0 sm:left-auto md:max-w-[420px] md:my-auto max-h-fit">
        <div
            class="relative h-fit min-h-40 p-5 overflow-y-scroll bg-white rounded-lg  dark:bg-slate-700 md:min-w-60 min-w-40 w-full max-w-lg mx-auto shadow-[-5px_-5px_5px_rgba(0,0,0,0.1)] md:my-auto max-h-[90vh]">
            <button @click="showModal = false;clearMapElements();emptySelected();$wire.set('vehicleDetails',null)"
                class="absolute top-3 right-3 dark:text-slate-400 text-slate-600">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                    stroke="currentColor" class="w-6 h-6 cursor-pointer">
                    <path stroke-linecap="round" stroke-linejoin="round"
                        d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
            </button>


            <div>

                <div class="flex flex-col gap-3 md:gap-5">
                    <div x-data="{ show: true }">
                        <h3 @click="show = !show"
                            class="flex items-center gap-2 text-base font-semibold cursor-pointer md:text-xl dark:text-slate-100 text-slate-800">
                            <svg width="12" height="13" class="transition-all duration-300"
                                :class="show ? 'rotate-0' : 'rotate-180'" viewBox="0 0 12 13" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <path d="M6 2.89999L0 9.29999H12L6 2.89999Z" fill="currentColor" />
                            </svg>

                            {{ $vehicleDetails['license_plate'] ?? '' }}
                        </h3>

                        <div x-show="show" x-cloak>

                            <span class="mt-2 text-sm dark:text-slate-300">
                                {{ $vehicleDetails['model'] ?? '' }}
                            </span>



                            @if (isset($vehicleDetails['driver']) && isset($vehicleDetails['driver']['name']))
                                <div class="flex items-center mt-1 space-x-1 text-gray-600 dark:text-slate-300">
                                    <span class="inline-flex items-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" fill="currentColor"
                                            viewBox="0 0 24 24">
                                            <path
                                                d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-3.31 0-6 2.69-6 6h12c0-3.31-2.69-6-6-6z">
                                            </path>
                                        </svg>
                                    </span>
                                    <span class="text-xs">
                                        {{ $vehicleDetails['driver']['name'] ?? '' }}
                                    </span>
                                </div>
                            @endif

                            <div x-data="{
                                speed: {{ $vehicleDetails['speed'] ?? 0 }},
                                maxSpeed: 200,
                                getRotation() {
                                    const angle = -135 + (this.speed / this.maxSpeed) * 270;
                                    return Math.max(-135, Math.min(135, angle));
                                },
                                getSpeedColor() {
                                    return this.speed > 120 ? 'text-red-500' : (this.speed > 80 ? 'text-yellow-500' : 'text-primary');
                                }
                            }" x-on:speed-updated.window="speed = $event.detail">



                                <div
                                    class="w-full p-4 mt-4 border shadow-lg dark:border-slate-700 bg-gray-50 dark:shadow-2xl dark:bg-slate-800 rounded-xl">


                                    <div class="grid grid-cols-3 gap-4">
                                        <!-- Daily Distance -->
                                        <div
                                            class="p-2 transition-all duration-300 border rounded-lg shadow-sm md:p-3 bg-gray-50 dark:bg-slate-800 dark:border-slate-700">

                                            <div class="flex items-baseline">
                                                <span class="text-lg font-bold text-slate-800 dark:text-slate-200">
                                                    {{ number_format($vehicleDetails['daily_distance'] ?? 0, 1) }}
                                                </span>
                                                <span class="ml-1 text-sm text-gray-500 dark:text-gray-400">km</span>
                                            </div>
                                            <div
                                                class="md:mt-2 mt-1 md:text-[11px] text-[9px] text-gray-500 dark:text-gray-400">
                                                @lang('translations.today_travel_distance')
                                            </div>
                                        </div>
                                        <!-- Speed Meter -->
                                        <div class="relative w-24 h-24 mx-auto" wire:ignore>
                                            <!-- Background Circle -->
                                            <div
                                                class="absolute inset-0 border-[12px] border-gray-200 rounded-full dark:border-gray-700/30">
                                            </div>

                                            <!-- Progress Circle -->
                                            <div class="absolute inset-0 transition-all duration-300"
                                                :style="`background: conic-gradient(var(--primary-color) ${(speed / maxSpeed) * 270}deg, transparent 0deg); transform: rotate(-135deg); border-radius: 50%;`">
                                            </div>


                                            <!-- Speed Display -->
                                            <div
                                                class="absolute inset-0 z-10 flex flex-col items-center justify-center">
                                                <span class="text-2xl font-bold transition-colors duration-300"
                                                    :class="getSpeedColor()" x-text="speed"></span>
                                                <span class="mt-1 text-xs font-medium text-gray-400">km/h</span>
                                            </div>

                                            <!-- Speed Indicator -->
                                            <div class="absolute inset-0 transition-transform duration-300"
                                                :style="`transform: rotate(${getRotation()}deg)`">
                                                <div
                                                    class="absolute w-1 h-8 origin-bottom transform -translate-x-1/2 rounded-full shadow-lg bg-primary left-1/2 bottom-1/2">
                                                </div>
                                            </div>
                                            <!-- Speed Markers -->
                                            <template x-for="i in 19" :key="i">
                                                <div class="absolute inset-0"
                                                    :style="`transform: rotate(${(i - 1) * 15 - 135}deg)`">
                                                    <div :class="(i - 1) % 3 === 0 ? 'h-3 w-1' : 'h-2 w-0.5'"
                                                        class="absolute top-0 transition-colors duration-300 transform -translate-x-1/2 left-1/2"
                                                        :class="(i - 1) * 15 <= (speed / maxSpeed) * 270 ?
                                                            'bg-primary/60 dark:bg-primary/60' :
                                                            'bg-gray-300 dark:bg-gray-600'">
                                                    </div>
                                                </div>
                                            </template>
                                            <!-- Speed Labels -->
                                            <div class="absolute inset-0">
                                                <!-- 0 km/h -->
                                                <span
                                                    class="absolute text-[8px] font-medium text-gray-500 -translate-x-1/2 -translate-y-1/2 dark:text-gray-400"
                                                    style="left: 15%; top: 76%">0</span>
                                                <!-- 50 km/h -->
                                                <span
                                                    class="absolute text-[8px] font-medium text-gray-500 -translate-x-1/2 -translate-y-1/2 dark:text-gray-400"
                                                    style="left: 8%; top: 30%">50</span>
                                                <!-- 100 km/h -->
                                                <span
                                                    class="absolute text-[8px] font-medium text-gray-500 -translate-x-1/2 -translate-y-1/2 dark:text-gray-400"
                                                    style="left: 50%; top: 5%">100</span>
                                                <!-- 150 km/h -->
                                                <span
                                                    class="absolute text-[8px] font-medium text-gray-500 -translate-x-1/2 -translate-y-1/2 dark:text-gray-400"
                                                    style="left: 90%; top: 30%">150</span>
                                                <!-- 200 km/h -->
                                                <span
                                                    class="absolute text-[8px] font-medium text-gray-500 -translate-x-1/2 -translate-y-1/2 dark:text-gray-400"
                                                    style="left: 85%; top: 75%">200</span>
                                            </div>
                                        </div>

                                        <!-- Total Odometer -->
                                        <div
                                            class="p-2 transition-all duration-300 border rounded-lg shadow-sm md:p-3 bg-gray-50 dark:bg-slate-800 dark:border-slate-700">

                                            <div class="flex items-baseline">
                                                <span class="text-lg font-bold text-slate-800 dark:text-slate-200">
                                                    {{ number_format($vehicleDetails['odometer'] ?? 0, 0) }}
                                                </span>
                                                <span class="ml-1 text-sm text-gray-500 dark:text-gray-400">km</span>
                                            </div>
                                            <div
                                                class="md:mt-2 mt-1 md:text-[11px] text-[9px] text-gray-500 dark:text-gray-400">
                                                @lang('translations.total_distance_traveled')
                                            </div>
                                        </div>
                                    </div>


                                    @if (isset($vehicleDetails['fuel_level_percentage']) && !empty($vehicleDetails['fuel_level_percentage']))
                                        <div x-data="{
                                            fuelLevel: {{ $vehicleDetails['fuel_level_percentage'] ?? 0 }},
                                            getFuelColor() {
                                                return this.fuelLevel > 50 ? 'bg-green-500' : (this.fuelLevel > 20 ? 'bg-yellow-500' : 'bg-red-500');
                                            },
                                            getFuelTextColor() {
                                                return this.fuelLevel > 50 ? 'text-green-600 dark:text-green-400' :
                                                    (this.fuelLevel > 20 ? 'text-yellow-600 dark:text-yellow-400' : 'text-red-600 dark:text-red-400');
                                            },
                                            getFuelMessage() {
                                                if (this.fuelLevel > 50) return '@lang('translations.fuel_level_good')';
                                                if (this.fuelLevel > 20) return '@lang('translations.fuel_level_warning')';
                                                return '@lang('translations.fuel_level_critical')';
                                            }
                                        }"
                                            x-on:fuel-level-updated.window="fuelLevel = $event.detail"
                                            class="flex flex-col gap-2 mt-3">
                                            <div class="flex items-center justify-between">
                                                <h4 class="text-slate-600 dark:text-slate-300">
                                                    @lang('translations.fuel_level')
                                                </h4>
                                                <p class="font-medium dark:text-slate-100" x-text="fuelLevel + '%'">
                                                </p>
                                            </div>

                                            <div
                                                class="relative w-full h-4 overflow-hidden bg-gray-200 rounded-full dark:bg-slate-700">
                                                <div class="absolute top-0 left-0 h-full transition-all duration-300 rounded-full"
                                                    :class="getFuelColor()" :style="`width: ${fuelLevel}%`">
                                                </div>

                                                <div class="absolute transition-all duration-300 transform -translate-y-1/2 -top-5"
                                                    :style="`left: calc(${fuelLevel}% - 12px)`">
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6"
                                                        :class="fuelLevel > 50 ? 'text-green-600' : (fuelLevel > 20 ?
                                                            'text-yellow-600' : 'text-red-600')"
                                                        fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2"
                                                            d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                                    </svg>
                                                </div>
                                            </div>

                                            <div
                                                class="flex justify-between px-1 text-xs text-slate-500 dark:text-slate-400">
                                                <span>E</span>
                                                <span>1/4</span>
                                                <span>1/2</span>
                                                <span>3/4</span>
                                                <span>F</span>
                                            </div>

                                            <div class="text-center dark:text-slate-100">
                                                <div class="mt-1 text-sm" :class="getFuelTextColor()"
                                                    x-text="getFuelMessage()"></div>
                                            </div>
                                        </div>
                                    @endif

                                </div>
                            </div>

                            <div class="flex flex-wrap items-center justify-center w-full gap-3 mt-3">
                                @if (isset($vehicleDetails['indicators']['ignition']['available']) &&
                                        $vehicleDetails['indicators']['ignition']['available']
                                )
                                    <!-- Ignition Indicator -->
                                    <div x-data="{ tooltip: false }" @mouseenter="tooltip = true"
                                        @mouseleave="tooltip = false" class="relative">
                                        <div
                                            class="flex items-center px-3 py-2 bg-white rounded-lg shadow-sm dark:bg-slate-800">
                                            <x-icons.ignition :color="$vehicleDetails['indicators']['ignition']['value'] == 1
                                                ? '#22c55e'
                                                : '#f02424'" />
                                        </div>
                                        <div x-show="tooltip" x-transition:enter="transition ease-out duration-200"
                                            x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100"
                                            x-transition:leave="transition ease-in duration-150"
                                            x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0"
                                            class="absolute z-10 px-3 py-1 mb-2 text-sm text-white bg-gray-900 rounded shadow-lg bottom-full whitespace-nowrap"
                                            style="left: 50%; transform: translateX(-50%);">
                                            @lang('translations.ignition_status'):
                                            {{ $vehicleDetails['indicators']['ignition']['value'] == 1 ? __('translations.on') : __('translations.off') }}
                                        </div>
                                    </div>
                                @endif

                                @if (isset($vehicleDetails['indicators']['parked']['available']) && $vehicleDetails['indicators']['parked']['available'])
                                    <!-- Parked Indicator -->
                                    @php
                                        $isParked =
                                            $vehicleDetails['indicators']['parked']['movement'] == 0 ||
                                            $vehicleDetails['indicators']['parked']['speed'] == 0;
                                    @endphp
                                    <div x-data="{ tooltip: false }" @mouseenter="tooltip = true"
                                        @mouseleave="tooltip = false" class="relative">
                                        <div
                                            class="flex items-center px-3 py-2 bg-white rounded-lg shadow-sm dark:bg-slate-800">
                                            @if (!$isParked)
                                                <x-icons.steering :color="$isParked ? '#f02424' : '#22c55e'" />
                                            @else
                                                <x-icons.parked :color="$isParked ? '#f02424' : '#22c55e'" />
                                            @endif
                                        </div>
                                        <div x-show="tooltip" x-transition:enter="transition ease-out duration-200"
                                            x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100"
                                            x-transition:leave="transition ease-in duration-150"
                                            x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0"
                                            class="absolute z-10 px-3 py-1 mb-2 text-sm text-white bg-gray-900 rounded shadow-lg bottom-full whitespace-nowrap"
                                            style="left: 50%; transform: translateX(-50%);">
                                            @lang('translations.movement_status'):
                                            {{ $isParked ? __('translations.parked') : __('translations.moving') }}
                                        </div>
                                    </div>
                                @endif

                                {{-- @if (isset($vehicleDetails['indicators']['battery']['available']) && $vehicleDetails['indicators']['battery']['available'])
                                    <!-- Battery Indicator -->
                                    @php
                                        $batteryVoltage = $vehicleDetails['indicators']['battery']['voltage'] ?? 0;
                                        $batteryLevel = 0;
                                        $batteryColor = '#ef4444'; // Red for low battery

                                        if ($batteryVoltage > 0) {
                                            // Typical car battery is 12-14V
                                            if ($batteryVoltage >= 13) {
                                                $batteryLevel = 100;
                                                $batteryColor = '#22c55e'; // Green for good
                                            } elseif ($batteryVoltage >= 12) {
                                                $batteryLevel = 75;
                                                $batteryColor = '#22c55e'; // Green for good
                                            } elseif ($batteryVoltage >= 11) {
                                                $batteryLevel = 50;
                                                $batteryColor = '#f02424'; // Yellow for medium
                                            } elseif ($batteryVoltage >= 10) {
                                                $batteryLevel = 25;
                                                $batteryColor = '#ef4444'; // Red for low
                                            }
                                        }
                                    @endphp
                                    <div x-data="{ tooltip: false }" @mouseenter="tooltip = true"
                                        @mouseleave="tooltip = false" class="relative">
                                        <div
                                            class="flex items-center px-3 py-2 bg-white rounded-lg shadow-sm dark:bg-slate-800">
                                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"
                                                class="flex-shrink-0 w-auto h-6" fill="{{ $batteryColor }}">
                                                <path
                                                    d="M15.67 4H14V2h-4v2H8.33C7.6 4 7 4.6 7 5.33v15.33C7 21.4 7.6 22 8.33 22h7.33c.74 0 1.34-.6 1.34-1.33V5.33C17 4.6 16.4 4 15.67 4z" />
                                                @if ($batteryLevel <= 25)
                                                    <path d="M7 10.5h10v3H7z" fill="#fff" />
                                                @elseif($batteryLevel <= 50)
                                                    <path d="M7 9h10v6H7z" fill="#fff" />
                                                @elseif($batteryLevel <= 75)
                                                    <path d="M7 7.5h10v9H7z" fill="#fff" />
                                                @endif
                                            </svg>
                                        </div>
                                        <div x-show="tooltip" x-transition:enter="transition ease-out duration-200"
                                            x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100"
                                            x-transition:leave="transition ease-in duration-150"
                                            x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0"
                                            class="absolute z-10 px-3 py-1 mb-2 text-sm text-white bg-gray-900 rounded shadow-lg bottom-full whitespace-nowrap"
                                            style="left: 50%; transform: translateX(-50%);">
                                            @lang('translations.battery_status'): {{ number_format($batteryVoltage, 1) }}V
                                        </div>
                                    </div>
                                @endif --}}

                                @if (isset($vehicleDetails['indicators']['signal']['available']) && $vehicleDetails['indicators']['signal']['available'])
                                    <!-- Signal Strength Indicator -->
                                    @php
                                        $signalValue = $vehicleDetails['indicators']['signal']['value'] ?? 0;
                                        $signalValue = min(5, max(0, (int) $signalValue)); // Ensure value is between 0-5

                                        // Define colors based on signal strength
                                        if ($signalValue >= 4) {
                                            $signalColor = 'bg-emerald-500'; // Green for excellent
                                        } elseif ($signalValue >= 3) {
                                            $signalColor = 'bg-green-500'; // Green for good
                                        } elseif ($signalValue >= 2) {
                                            $signalColor = 'bg-amber-500'; // Yellow for medium
                                        } else {
                                            $signalColor = 'bg-red-500'; // Red for low
                                        }
                                    @endphp
                                    <div x-data="{ tooltip: false }" @mouseenter="tooltip = true"
                                        @mouseleave="tooltip = false" class="relative">
                                        <div
                                            class="flex items-center gap-1 px-3 py-2 bg-white rounded-lg shadow-sm dark:bg-slate-800">
                                            <div class="flex items-end h-6 gap-[2px]">
                                                <!-- Signal Bar 1 (Always visible if signal > 0) -->
                                                <div
                                                    class="w-1 h-1 rounded-sm {{ $signalValue >= 1 ? $signalColor : 'bg-gray-300 dark:bg-gray-600' }}">
                                                </div>

                                                <!-- Signal Bar 2 -->
                                                <div
                                                    class="w-1 h-2 rounded-sm {{ $signalValue >= 2 ? $signalColor : 'bg-gray-300 dark:bg-gray-600' }}">
                                                </div>

                                                <!-- Signal Bar 3 -->
                                                <div
                                                    class="w-1 h-3 rounded-sm {{ $signalValue >= 3 ? $signalColor : 'bg-gray-300 dark:bg-gray-600' }}">
                                                </div>

                                                <!-- Signal Bar 4 -->
                                                <div
                                                    class="w-1 h-4 rounded-sm {{ $signalValue >= 4 ? $signalColor : 'bg-gray-300 dark:bg-gray-600' }}">
                                                </div>

                                                <!-- Signal Bar 5 -->
                                                <div
                                                    class="w-1 h-5 rounded-sm {{ $signalValue >= 5 ? $signalColor : 'bg-gray-300 dark:bg-gray-600' }}">
                                                </div>
                                            </div>
                                        </div>
                                        <div x-show="tooltip" x-transition:enter="transition ease-out duration-200"
                                            x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100"
                                            x-transition:leave="transition ease-in duration-150"
                                            x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0"
                                            class="absolute z-10 px-3 py-1 mb-2 text-sm text-white bg-gray-900 rounded shadow-lg bottom-full whitespace-nowrap"
                                            style="left: 50%; transform: translateX(-50%);">
                                            @lang('translations.signal_strength'): {{ $signalValue }}/5
                                        </div>
                                    </div>
                                @endif

                                @if (!empty($vehicleDetails['geofence_status']))
                                    <!-- Geofence Status Indicator -->
                                    @php
                                        $insideAnyGeofence = false;
                                        foreach ($vehicleDetails['geofence_status'] as $status) {
                                            if ($status['inside']) {
                                                $insideAnyGeofence = true;
                                                break;
                                            }
                                        }
                                        $geofenceColor = $insideAnyGeofence ? '#22c55e' : '#ef4444';
                                    @endphp
                                    <div x-data="{ tooltip: false }" @mouseenter="tooltip = true"
                                        @mouseleave="tooltip = false" class="relative">
                                        <div
                                            class="flex items-center px-3 py-2 bg-white rounded-lg shadow-sm dark:bg-slate-800">
                                            <x-icons.geofence :color="$geofenceColor" />
                                        </div>
                                        <div x-show="tooltip" x-transition:enter="transition ease-out duration-200"
                                            x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100"
                                            x-transition:leave="transition ease-in duration-150"
                                            x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0"
                                            class="absolute z-10 px-3 py-1 mb-2 text-sm text-white bg-gray-900 rounded shadow-lg bottom-full whitespace-nowrap"
                                            style="left: 50%; transform: translateX(-50%);">
                                            @lang('translations.geofence_status'):
                                            {{ $insideAnyGeofence ? __('translations.inside') : __('translations.outside') }}
                                        </div>
                                    </div>
                                @endif
                            </div>

                        </div>


                    </div>



                    <div>
                        @can('fleet_view_history_mode')
                            <div class="flex items-center justify-center mt-3">
                                <span class="text-sm font-medium dark:text-slate-300">
                                    {{ __('translations.history_mode') }}
                                </span>
                                <label class="toggle-switch">
                                    <input type="checkbox" id="liveModeToggle" wire:model.live="liveMode" checked>
                                    <div class="toggle-switch-background">
                                        <div class="toggle-switch-handle"></div>
                                    </div>
                                </label>
                                <span class="text-sm font-medium dark:text-slate-300">
                                    {{ __('translations.live_mode') }}
                                </span>
                            </div>
                            <div class="flex items-center justify-center w-full my-3">
                                {{-- history mode spinner --}}
                                <div wire:loading wire:target="liveMode"
                                    class="w-8 h-8 border-b-2 rounded-full animate-spin border-primary"></div>

                                <div wire:loading wire:target="selectedDate"
                                    class="w-8 h-8 border-b-2 rounded-full animate-spin border-primary"></div>
                            </div>

                            @if (!$liveMode)
                                <div wire:ignore id="historyControls" class="mt-3">
                                    <div class="flex flex-col items-center justify-center gap-4 md:flex-nowrap">
                                        <select wire:model.live="selectedDate" id="dateDropdown"
                                            class="peer py-2 px-3 block w-fit min-w-36 border-[1.5px] border-[#D5D7DA] shadow-sm rounded-lg text-sm focus:border-primary outline-none transition-all duration-300 dark:bg-slate-700 dark:text-white dark:border-slate-500">
                                            <option value=""></option>
                                            @if (!empty($dates))
                                                @foreach ($dates as $date)
                                                    <option wire:key="date-{{ $date }}"
                                                        value="{{ $date }}">
                                                        {{ $date }}</option>
                                                @endforeach
                                            @endif
                                        </select>

                                        <div class="flex items-center gap-2">
                                            <div id="seek-container">
                                                <span id="currentTime" class="md:whitespace-nowrap">

                                                </span>
                                                <input type="range" id="seekbar" min="0" max="100"
                                                    value="0" oninput="seekToPosition(this.value)">
                                            </div>

                                            <button onclick="playPause()"
                                                class="flex items-center justify-center px-4 py-2 text-sm font-medium text-white transition duration-300 ease-in-out transform rounded-lg shadow-lg bg-primary hover:bg-primaryDark hover:scale-105">

                                                <svg id="playIcon" xmlns="http://www.w3.org/2000/svg"
                                                    viewBox="0 0 24 24" fill="currentColor" class="size-5 me-2">
                                                    <path fill-rule="evenodd"
                                                        d="M4.5 5.653c0-1.427 1.529-2.33 2.779-1.643l11.54 6.347c1.295.712 1.295 2.573 0 3.286L7.28 19.99c-1.25.687-2.779-.217-2.779-1.643V5.653Z"
                                                        clip-rule="evenodd" />
                                                </svg>

                                                <svg id="pauseIcon" xmlns="http://www.w3.org/2000/svg"
                                                    viewBox="0 0 24 24" fill="currentColor" class="hidden size-6 me-2">
                                                    <path fill-rule="evenodd"
                                                        d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12ZM9 8.25a.75.75 0 0 0-.75.75v6c0 .414.336.75.75.75h.75a.75.75 0 0 0 .75-.75V9a.75.75 0 0 0-.75-.75H9Zm5.25 0a.75.75 0 0 0-.75.75v6c0 .414.336.75.75.75H15a.75.75 0 0 0 .75-.75V9a.75.75 0 0 0-.75-.75h-.75Z"
                                                        clip-rule="evenodd" />
                                                </svg>

                                                <span id="buttonText">{{ __('translations.play') }}<span>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            @endif
                        @endcan

                    </div>


                </div>

                <div x-data="{ show: false }">

                    <h2 @click="show = !show"
                        class="flex items-center gap-2 mt-4 mb-4 font-semibold text-gray-800 cursor-pointer md:text-xl md:mt-8 dark:text-slate-100">
                        <svg width="12" height="13" class="transition-all duration-300"
                            :class="show ? 'rotate-0' : 'rotate-180'" viewBox="0 0 12 13" fill="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <path d="M6 2.89999L0 9.29999H12L6 2.89999Z" fill="currentColor" />
                        </svg>
                        @lang('translations.show_details')

                    </h2>


                    <div x-show="show" x-cloak class="grid grid-cols-1 mt-5 text-sm gap-x-2 gap-y-3">
                        <div wire:ignore class="flex flex-col gap-2">

                            <h4 class="text-slate-600 dark:text-slate-300">
                                @lang('translations.address')

                            </h4>

                            <p id="address" class="font-medium dark:text-slate-100 text-wrap ">
                                {{ $vehicleDetails['address'] ?? '' }}

                            </p>
                        </div>

                        @if (isset($vehicleDetails['stops']))
                            <div x-data="{ show: $wire.entangle('showStops').live }"
                                class="p-4 mt-4 bg-white rounded-lg shadow-sm dark:bg-slate-800">
                                <h3 x-on:click="show = !show"
                                    class="flex items-center gap-2 mb-4 text-lg font-medium cursor-pointer text-slate-800 dark:text-slate-200">
                                    {{-- up icon --}}
                                    <svg width="12" height="13" class="transition-all duration-300"
                                        :class="show ? 'rotate-0' : 'rotate-180'" viewBox="0 0 12 13" fill="none"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path d="M6 2.89999L0 9.29999H12L6 2.89999Z" fill="currentColor" />
                                    </svg>
                                    @lang('translations.today_stops')
                                </h3>

                                <div class="p-3 mb-4 rounded-md bg-slate-50 dark:bg-slate-700">
                                    <div class="grid grid-cols-2 gap-2">
                                        <div class="flex items-center gap-2">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="size-5 text-primary"
                                                viewBox="0 0 24 24" fill="currentColor">
                                                <path
                                                    d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z" />
                                            </svg>
                                            <p class="text-sm text-slate-600 dark:text-slate-300">
                                                @lang('translations.stops'): <span
                                                    class="font-medium">{{ $vehicleDetails['total_stops'] }}</span>
                                            </p>
                                        </div>
                                        <div class="flex items-center gap-2">
                                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"
                                                fill="currentColor" class="size-5 text-primary">
                                                <path fill-rule="evenodd"
                                                    d="M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25ZM12.75 6a.75.75 0 0 0-1.5 0v6c0 .414.336.75.75.75h4.5a.75.75 0 0 0 0-1.5h-3.75V6Z"
                                                    clip-rule="evenodd" />
                                            </svg>

                                            <p class="text-sm text-slate-600 dark:text-slate-300">
                                                @lang('translations.duration'): <span
                                                    class="font-medium">{{ floor($vehicleDetails['total_stop_time'] / 60) }}h
                                                    {{ $vehicleDetails['total_stop_time'] % 60 }}m</span>
                                            </p>
                                        </div>
                                    </div>
                                </div>

                                <div x-show="show" class="space-y-2">
                                    @foreach ($vehicleDetails['stops'] as $index => $stop)
                                        <div wire:key="stop-{{ $index }}"
                                            class="p-4 transition-all duration-200 border rounded-lg shadow-sm border-slate-200 hover:border-primary/30 hover:shadow-md dark:border-slate-600 dark:hover:border-primary/30 dark:hover:bg-slate-700/50">
                                            <div class="relative flex gap-3">

                                                <div
                                                    class="absolute flex flex-col items-center gap-2 -top-1 left-2 bottom-4">
                                                    <div class="w-0.5 h-full dark:bg-slate-600/50 bg-slate-200">
                                                    </div>
                                                    <div
                                                        class="absolute flex flex-col items-center justify-center flex-shrink-0 w-full h-full text-center">
                                                        <span
                                                            class="px-2 py-1 text-[10px] leading-3 font-medium rounded-full text-slate-600 bg-slate-100 dark:bg-slate-700 dark:text-slate-300">
                                                            @if (isset($stop['trip_distance']) && $stop['trip_distance'])
                                                                {{ $stop['trip_distance'] ?? '' }}
                                                            @endif
                                                        </span>
                                                    </div>
                                                </div>

                                                <!-- Location marker and address -->
                                                <div class="flex gap-3 ps-10">
                                                    <div class="flex flex-col gap-1">
                                                        <p
                                                            class="text-sm font-medium text-slate-700 dark:text-slate-200">
                                                            {{ $stop['location']['address'] ?? '' }}
                                                        </p>

                                                        <!-- Time and duration info -->
                                                        <div class="flex items-center gap-3 mt-2">
                                                            <div
                                                                class="flex items-center gap-2 text-sm text-slate-600 dark:text-slate-400">
                                                                <svg xmlns="http://www.w3.org/2000/svg" class="size-4"
                                                                    fill="none" viewBox="0 0 24 24"
                                                                    stroke="currentColor">
                                                                    <path stroke-linecap="round"
                                                                        stroke-linejoin="round" stroke-width="2"
                                                                        d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                                </svg>
                                                                <span>
                                                                    {{ parseFlexibleTimestamp($stop['start_time'])->format('H:i') }}
                                                                    -
                                                                    {!! $stop['end_time'] === 'Ongoing'
                                                                        ? '<span class="px-2 py-1 text-xs font-medium rounded-full text-primary bg-primary/10">' .
                                                                            __('translations.ongoing') .
                                                                            '</span>'
                                                                        : parseFlexibleTimestamp($stop['end_time'])->format('H:i') !!}
                                                                </span>
                                                            </div>
                                                            <span
                                                                class="px-3 py-1 text-xs font-medium rounded-full ms-auto text-primary bg-primary/10 dark:bg-primary/5">
                                                                {{ $stop['duration'] }}
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        @endif



                        @if (isset($vehicleDetails['daily_fuel']) && $vehicleDetails['daily_fuel'] > 0)
                            <div class="flex justify-between gap-2">

                                <h4 class="text-slate-600 dark:text-slate-300">
                                    @lang('translations.today_fuel_consumption')

                                </h4>

                                <p class="font-medium dark:text-slate-100 text-end">
                                    {{ $vehicleDetails['daily_fuel'] ?? '' }} l
                                </p>
                            </div>
                        @endif



                        @if (isset($vehicleDetails['fuel_level']) && $vehicleDetails['fuel_level'] > 0)
                            <div class="flex justify-between gap-2">

                                <h4 class="text-slate-600 dark:text-slate-300">
                                    @lang('translations.fuel_level')

                                </h4>

                                <p class="font-medium dark:text-slate-100 text-end">
                                    {{ $vehicleDetails['fuel_level'] ?? '' }} l
                                </p>
                            </div>
                        @endif

                        @if (isset($vehicleDetails['average_fuel']) && $vehicleDetails['average_fuel'] > 0)
                            <div class="flex justify-between gap-2">

                                <h4 class="text-slate-600 dark:text-slate-300">
                                    @lang('translations.average_consumption')

                                </h4>

                                <p class="font-medium dark:text-slate-100 text-end">
                                    {{ $vehicleDetails['average_fuel'] ?? '' }} l/100km
                                </p>
                            </div>
                        @endif


                        <div class="flex justify-between gap-2">

                            {{-- @php
                                        $distance = $vehicleDetails['odometer'] ?? 0;
                                        $vehicleType = $vehicleDetails['type'] ?? null;
                                        $mileage = 0;

                                        // Define mileage values based on vehicle type
                                        switch ($vehicleType) {
                                            case 'truck':
                                                $mileage = 3; // 3 km/l for heavy vehicles
                                                break;
                                            case 'bus':
                                                $mileage = 3; // 3 km/l for heavy vehicles
                                                break;
                                            case 'tractor':
                                                $mileage = 3; // 3 km/l for heavy vehicles
                                                break;

                                            case 'car':
                                                $mileage = 11; // 3 km/l for heavy vehicles
                                                break;
                                            case 'bike':
                                                $mileage = 11; // 3 km/l for heavy vehicles
                                                break;
                                            case 'scooter':
                                                $mileage = 11; // 3 km/l for heavy vehicles
                                                break;
                                            case 'van':
                                                $mileage = 11; // 3 km/l for heavy vehicles
                                                break;
                                            case 'ambulance':
                                                $mileage = 11; // 11 km/l for light vehicles
                                                break;

                                            case 'cycle':
                                                $mileage = 0; // No fuel required or undefined mileage
                                                break;
                                            case 'boat':
                                                $mileage = 0; // No fuel required or undefined mileage
                                                break;
                                            case 'other':
                                                $mileage = 0; // No fuel required or undefined mileage
                                                break;

                                            default:
                                                $mileage = 0; // Fallback for undefined vehicle types
                                                break;
                                        }

                                        // Calculate fuel consumption
                                        $fuelConsumption = $mileage > 0 ? round($distance / $mileage, 2) : 0;
                                    @endphp

                                    @if ($fuelConsumption)
                                        <h4 class="text-slate-600 dark:text-slate-300">
                                            @lang('translations.fuel_consumption')

                                        </h4>
                                        <p class="font-medium dark:text-slate-100 text-end">
                                            {{ $fuelConsumption }} @lang('translations.liters')
                                        </p>
                                    @endif --}}

                        </div>

                        @if (isset($vehicleDetails['fuel_used']) && $vehicleDetails['fuel_used'] > 0)
                            <div class="flex justify-between gap-2">

                                <h4 class="text-slate-600 dark:text-slate-300">
                                    @lang('translations.total_fuel_consumption')

                                </h4>

                                <p class="font-medium dark:text-slate-100 text-end">
                                    {{ $vehicleDetails['fuel_used'] ?? '' }} l
                                </p>
                            </div>
                        @endif

                        @if (isset($vehicleDetails['remaining_distance']) && $vehicleDetails['remaining_distance'] > 0)
                            <div class="flex justify-between gap-2">

                                <h4 class="text-slate-600 dark:text-slate-300">
                                    @lang('translations.remaining_distance')

                                </h4>

                                <p class="font-medium dark:text-slate-100 text-end">
                                    {{ $vehicleDetails['remaining_distance'] ?? '' }} km
                                </p>
                            </div>
                        @endif



                        @if (isset($vehicleDetails['current_route']) && !empty($vehicleDetails['current_route']))
                            <div class="flex justify-between gap-2">

                                <h4 class="text-slate-600 dark:text-slate-300">
                                    @lang('translations.departure_time')
                                </h4>

                                <p class="font-medium dark:text-slate-100 text-end">
                                    {{ $vehicleDetails['current_route']['departure_time'] ?? '' }}
                                </p>
                            </div>

                            <div class="flex justify-between gap-2">

                                <h4 class="text-slate-600 dark:text-slate-300">
                                    @lang('translations.arrival_time')

                                </h4>

                                <p class="font-medium dark:text-slate-100 text-end">
                                    @php
                                        $departureTime = $vehicleDetails['current_route']['departure_time'] ?? null;
                                        $duration = $vehicleDetails['current_route']['duration'] ?? null;

                                        if ($departureTime && $duration) {
                                            // Parse the departure time
                                            $departure = \Carbon\Carbon::parse($departureTime);

                                            // Ensure duration is a number
                                            $durationInMinutes = (int) $duration;

                                            // Add the duration to calculate arrival time
                                            $arrival = $departure->addMinutes($durationInMinutes);

                                            // Display the arrival time in HH:mm format
                                            echo $arrival->format('H:i');
                                        } else {
                                            echo 'N/A'; // Fallback if data is missing
                                        }
                                    @endphp

                                </p>

                            </div>

                            <div class="flex justify-between gap-2">

                                <h4 class="text-slate-600 dark:text-slate-300">
                                    @lang('translations.distance_traveled')
                                </h4>

                                <p class="font-medium dark:text-slate-100 text-end">
                                    {{ isset($vehicleDetails['current_route']['distance']) ? $vehicleDetails['current_route']['distance'] . ' km' : '' }}
                                </p>
                            </div>

                            <div class="flex justify-between gap-2">

                                <h4 class="text-slate-600 dark:text-slate-300">
                                    @lang('translations.trip_duration')


                                </h4>

                                <p class="font-medium dark:text-slate-100 text-end">
                                    @if (isset($vehicleDetails['current_route']['duration']))
                                        @formatDuration($vehicleDetails['current_route']['duration'] ?? 0)
                                    @endif
                                </p>
                            </div>
                            <div class="flex justify-between gap-2">


                                <h4 class="text-slate-600 dark:text-slate-300">
                                    @lang('translations.route_fuel_consumption')

                                </h4>

                                <p class="font-medium dark:text-slate-100 text-end">
                                    @php
                                        $distance = $vehicleDetails['current_route']['distance'] ?? 0;
                                        $vehicleType = $vehicleDetails['type'] ?? null;
                                        $mileage = 0;

                                        // Define mileage values based on vehicle type
                                        switch ($vehicleType) {
                                            case 'truck':
                                                $mileage = 3; // 3 km/l for heavy vehicles
                                                break;
                                            case 'bus':
                                                $mileage = 3; // 3 km/l for heavy vehicles
                                                break;
                                            case 'tractor':
                                                $mileage = 3; // 3 km/l for heavy vehicles
                                                break;

                                            case 'car':
                                                $mileage = 11; // 3 km/l for heavy vehicles
                                                break;
                                            case 'bike':
                                                $mileage = 11; // 3 km/l for heavy vehicles
                                                break;
                                            case 'scooter':
                                                $mileage = 11; // 3 km/l for heavy vehicles
                                                break;
                                            case 'van':
                                                $mileage = 11; // 3 km/l for heavy vehicles
                                                break;
                                            case 'ambulance':
                                                $mileage = 11; // 11 km/l for light vehicles
                                                break;

                                            case 'cycle':
                                                $mileage = 0; // No fuel required or undefined mileage
                                                break;
                                            case 'boat':
                                                $mileage = 0; // No fuel required or undefined mileage
                                                break;
                                            case 'other':
                                                $mileage = 0; // No fuel required or undefined mileage
                                                break;

                                            default:
                                                $mileage = 0; // Fallback for undefined vehicle types
                                                break;
                                        }

                                        // Calculate fuel consumption
                                        $fuelConsumption = $mileage > 0 ? round($distance / $mileage, 2) : 'N/A';
                                    @endphp

                                    {{ $fuelConsumption }} @lang('translations.liters')
                                </p>
                            </div>
                        @endif


                    </div>

                </div>


                {{-- @if (!isset($can_lock_unlock))
                            <div x-data="{ show: false }" class="mt-8 space-y-4">

                                <h3 @click="show = !show"
                                    class="flex items-center gap-2 text-base font-semibold cursor-pointer md:text-xl dark:text-slate-100 text-slate-800">
                                    <svg width="12" height="13" class="transition-all duration-300"
                                        :class="show ? 'rotate-0' : 'rotate-180'" viewBox="0 0 12 13" fill="none"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path d="M6 2.89999L0 9.29999H12L6 2.89999Z" fill="currentColor" />
                                    </svg>

                                    @lang('translations.engine_control')

                                </h3>

                                <div x-show="show" class="p-4 bg-gray-100 rounded-lg dark:bg-slate-800">
                                    <!-- Current Engine Status -->
                                    <div class="flex items-center gap-3 mb-4">
                                        <div class="flex items-center gap-2">
                                            <span class="rounded-full size-3"
                                                :class="{{ 'true' }} ? 'bg-red-500' : 'bg-green-500'">
                                            </span>
                                            <span class="text-sm dark:text-slate-200">
                                                Engine Status: <span
                                                    class="font-semibold">{{ 'true' ? 'Immobilized' : 'Active' }}</span>
                                            </span>
                                        </div>
                                        @if ('true')
                                            <svg xmlns="http://www.w3.org/2000/svg" class="text-red-500 size-5"
                                                viewBox="0 0 24 24" fill="currentColor">
                                                <path d="M3.75 13.5l10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75z" />
                                            </svg>
                                        @endif
                                    </div>

                                    <!-- Engine Control Buttons -->
                                    <div class="flex gap-3">
                                        <button wire:click="immobilizeEngine"
                                            class="flex items-center px-4 py-2 text-white transition-all duration-300 bg-red-500 rounded-md hover:bg-red-600">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="size-5 me-2"
                                                viewBox="0 0 24 24" fill="currentColor">
                                                <path d="M3.75 13.5l10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75z" />
                                            </svg>
                                            Immobilize Engine
                                        </button>

                                        <button wire:click="activateEngine"
                                            class="flex items-center px-4 py-2 text-white transition-all duration-300 bg-green-500 rounded-md hover:bg-green-600">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="size-5 me-2"
                                                viewBox="0 0 24 24" fill="currentColor">
                                                <path
                                                    d="M21.721 12.752a9.711 9.711 0 00-.945-5.003 12.754 12.754 0 01-4.339 2.708 18.991 18.991 0 01-.214 4.772 17.165 17.165 0 005.498-2.477zM14.634 15.55a17.324 17.324 0 00.332-4.647c-.952.227-1.945.347-2.966.347-1.021 0-2.014-.12-2.966-.347a17.515 17.515 0 00.332 4.647 17.385 17.385 0 005.268 0zM9.772 17.119a18.963 18.963 0 004.456 0A17.182 17.182 0 0112 21.724a17.18 17.18 0 01-2.228-4.605zM7.777 15.23a18.87 18.87 0 01-.214-4.774 12.753 12.753 0 01-4.34-2.708 9.711 9.711 0 00-.944 5.004 17.165 17.165 0 005.498 2.477zM21.356 14.752a9.765 9.765 0 01-7.478 6.817 18.64 18.64 0 001.988-4.718 18.627 18.627 0 005.49-2.098zM2.644 14.752c1.682.971 3.53 1.688 5.49 2.099a18.64 18.64 0 001.988 4.718 9.765 9.765 0 01-7.478-6.816zM13.878 2.43a9.755 9.755 0 016.116 3.986 11.267 11.267 0 01-3.746 2.504 18.63 18.63 0 00-2.37-6.49zM12 2.276a17.152 17.152 0 012.805 7.121c-.897.23-1.837.353-2.805.353-.968 0-1.908-.122-2.805-.353A17.151 17.151 0 0112 2.276zM10.122 2.43a18.629 18.629 0 00-2.37 6.49 11.266 11.266 0 01-3.746-2.504 9.754 9.754 0 016.116-3.985z" />
                                            </svg>
                                            Activate Engine
                                        </button>
                                    </div>

                                    <!-- Confirmation Modal -->
                                    <div x-data="{ showModal: false, action: '' }" x-cloak>
                                        <template x-teleport="body">
                                            <div x-show="showModal"
                                                class="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
                                                <div
                                                    class="w-full max-w-md p-6 bg-white rounded-lg shadow-xl dark:bg-slate-800">
                                                    <h3 class="mb-4 text-lg font-medium dark:text-slate-200"
                                                        x-text="action === 'immobilize' ? 'Confirm Engine Immobilization' : 'Confirm Engine Activation'">
                                                    </h3>

                                                    <p class="mb-4 text-sm text-gray-600 dark:text-slate-300">
                                                        Are you sure you want to <span
                                                            x-text="action === 'immobilize' ? 'immobilize' : 'activate'"></span>
                                                        the engine? This action requires PIN verification.
                                                    </p>

                                                    <div class="flex justify-end gap-3">
                                                        <button @click="showModal = false"
                                                            class="px-4 py-2 text-sm text-gray-600 transition-all duration-300 border rounded-md hover:bg-gray-100 dark:text-slate-300 dark:border-slate-600 dark:hover:bg-slate-700">
                                                            Cancel
                                                        </button>
                                                        <button @click="showModal = false"
                                                            class="px-4 py-2 text-sm text-white transition-all duration-300 rounded-md"
                                                            :class="action === 'immobilize' ?
                                                                'bg-red-500 hover:bg-red-600' :
                                                                'bg-green-500 hover:bg-green-600'">
                                                            Confirm
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </template>
                                    </div>
                                </div>
                            </div>
                        @endif --}}


                @if (isset($vehicleDetails['current_route']) && !empty($vehicleDetails['current_route']))
                    <div x-data="{ show: false }">

                        <h2 @click="show = !show"
                            class="flex items-center gap-2 mt-4 mb-4 font-semibold text-gray-800 cursor-pointer md:text-xl md:mt-8 dark:text-slate-100">
                            <svg width="12" height="13" class="transition-all duration-300"
                                :class="show ? 'rotate-0' : 'rotate-180'" viewBox="0 0 12 13" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <path d="M6 2.89999L0 9.29999H12L6 2.89999Z" fill="currentColor" />
                            </svg>
                            @lang('translations.trip_timeline')

                        </h2>

                        <div x-show="show" x-cloak class="grid grid-cols-2 text-sm gap-x-2 gap-y-3">
                            <h4 class="text-slate-600 dark:text-slate-300">
                                @lang('translations.starting_point')
                            </h4>

                            <p class="font-medium max-w-44 ms-auto dark:text-slate-100 text-end">
                                {{ $vehicleDetails['current_route']['start_point'] ?? '' }}
                            </p>

                            <h4 class="text-slate-600 dark:text-slate-300">
                                @lang('translations.arrival_point')
                            </h4>

                            <p class="font-medium dark:text-slate-100 text-end max-w-44 ms-auto">
                                {{ $vehicleDetails['current_route']['end_point'] ?? '' }}
                            </p>
                            <h4 class="text-slate-600 dark:text-slate-300">
                                @lang('translations.travel_distance')
                            </h4>

                            <p class="font-medium dark:text-slate-100 text-end">
                                {{ isset($vehicleDetails['current_route']['distance']) ? $vehicleDetails['current_route']['distance'] . ' km' : '' }}
                            </p>
                            <h4 class="text-slate-600 dark:text-slate-300">
                                @lang('translations.trip_duration')
                            </h4>

                            <p class="font-medium dark:text-slate-100 text-end">
                                @if (isset($vehicleDetails['current_route']['duration']))
                                    @formatDuration($vehicleDetails['current_route']['duration'] ?? 0)
                                @endif
                            </p>
                        </div>
                        <div x-show="show" x-cloak class="mt-5">
                            <div class="flex items-start space-x-4">
                                <p class="w-16 text-sm text-gray-600 dark:text-slate-300">
                                    {{ isset($vehicleDetails['current_route']['start_point_completed_at']) && !empty($vehicleDetails['current_route']['start_point_completed_at']) ? \Carbon\Carbon::parse($vehicleDetails['current_route']['start_point_completed_at'])->format('H:i') : '' }}
                                </p>
                                <div>
                                    <div
                                        class="w-4 h-4 border-2  @if (isset($vehicleDetails['current_route']['start_point_status']) &&
                                                $vehicleDetails['current_route']['start_point_status'] == 'pending') border-green-500
                                                    @else
                                                    bg-green-500 @endif rounded-full">
                                    </div>
                                    <div class="w-0.5 h-16 bg-[#E3E3E3] mx-auto"></div>
                                </div>
                                <div>

                                    <p style="max-width: 13rem"
                                        class="font-semibold text-gray-900 text-wrap ms-auto dark:text-slate-100">
                                        {{ $vehicleDetails['current_route']['start_point'] ?? '' }}</p>
                                </div>
                            </div>
                            {{-- stops --}}

                            @php
                                $stops = isset($vehicleDetails['current_route']['stops'])
                                    ? $vehicleDetails['current_route']['stops']
                                    : [];
                            @endphp
                            @foreach ($stops as $index => $stop)
                                <div wire:key="route-stop-{{ $stop['id'] ?? $index }}"
                                    class="flex items-start space-x-4">
                                    <p class="w-16 text-sm text-gray-600 dark:text-slate-300">
                                        {{ isset($stop['completed_at']) && !empty($stop['completed_at']) ? \Carbon\Carbon::parse($stop['completed_at'])->format('H:i') : '' }}
                                    </p>

                                    <div>
                                        <div
                                            class="w-4 h-4 border-2  @if ($stop['status'] == 'pending') border-green-500
                                                    @else
                                                    bg-green-500 @endif rounded-full">
                                        </div>
                                        <div class="w-0.5 h-16 bg-[#E3E3E3] mx-auto"></div>
                                    </div>
                                    <div>
                                        <p style="max-width: 13rem"
                                            class="font-semibold text-gray-900 text-wrap dark:text-slate-100">
                                            {{ $stop['stop_point'] ?? '' }}</p>
                                        {{-- <p class="text-sm text-gray-500 dark:text-slate-200">
                                                    @lang('translations.stopped')
                                                </p> --}}
                                    </div>
                                </div>
                            @endforeach

                            <div class="flex items-start space-x-4">
                                <p class="w-16 text-sm text-gray-600 dark:text-slate-300">
                                    {{ isset($vehicleDetails['current_route']['end_point_completed_at']) && !empty($vehicleDetails['current_route']['end_point_completed_at']) ? \Carbon\Carbon::parse($vehicleDetails['current_route']['end_point_completed_at'])->format('H:i') : '' }}
                                </p>

                                <div>
                                    <div
                                        class="w-4 h-4 border-2
                                                @if (isset($vehicleDetails['current_route']['end_point_status']) &&
                                                        $vehicleDetails['current_route']['end_point_status'] == 'pending') border-green-500
                                                    @else
                                                    bg-green-500 @endif rounded-full">
                                    </div>
                                </div>
                                <div>
                                    <p style="max-width: 13rem"
                                        class="font-semibold text-gray-900 ms-auto dark:text-slate-100">
                                        {{ $vehicleDetails['current_route']['end_point'] ?? '' }}</p>
                                    {{-- <p class="text-sm text-gray-500 dark:text-slate-200">@lang('translations.parked')
                                            </p> --}}
                                </div>
                            </div>
                        </div>

                    </div>
                @endif

            </div>
        </div>
    </div>
</div>
