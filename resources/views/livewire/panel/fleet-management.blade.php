@push('styles')
    <style>
        /* Modern Slider Styles */
        .slider {
            -webkit-appearance: none;
            appearance: none;
            background: transparent;
            cursor: pointer;
        }

        .slider::-webkit-slider-track {
            background: #e2e8f0;
            height: 6px;
            border-radius: 3px;
        }

        .dark .slider::-webkit-slider-track {
            background: #475569;
        }

        .slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            height: 18px;
            width: 18px;
            border-radius: 50%;
            background: #F54619;
            cursor: pointer;
            border: 2px solid #ffffff;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            transition: all 0.2s ease;
        }

        .slider::-webkit-slider-thumb:hover {
            transform: scale(1.05);
            box-shadow: 0 3px 6px rgba(245, 70, 25, 0.3);
        }

        .slider::-moz-range-thumb {
            height: 18px;
            width: 18px;
            border-radius: 50%;
            background: #F54619;
            cursor: pointer;
            border: 2px solid #ffffff;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            transition: all 0.2s ease;
        }

        .slider::-moz-range-thumb:hover {
            transform: scale(1.05);
            box-shadow: 0 3px 6px rgba(245, 70, 25, 0.3);
        }

        #currentTime {
            color: white;
            font-size: 14px;
        }


        #map-btn,
        #close-geofence-btn {
            cursor: pointer;
            background: #f44336;
            color: white;
            border: none;

            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1111;

            right: 50px;
            width: fit-content;
            display: flex;
            align-items: center;
            border-radius: 6px;
            font-weight: bold;
            font-size: 11px;
            padding: 4px 5px;
            gap: 3px;

        }



        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 25px;
            cursor: pointer;
            margin: 0px 30px;
        }

        .toggle-switch input[type="checkbox"] {
            display: none;
        }

        .toggle-switch-background {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: #ddd;
            border-radius: 20px;
            box-shadow: inset 0 0 0 2px #ccc;
            transition: background-color 0.3s ease-in-out;
        }

        .toggle-switch-handle {
            position: absolute;
            top: 0px;
            left: 0px;
            width: 25px;
            height: 25px;
            background-color: #fff;
            border-radius: 50%;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
            transition: transform 0.3s ease-in-out;
        }

        .toggle-switch::before {
            content: "";
            position: absolute;
            top: -25px;
            right: -35px;
            font-size: 12px;
            font-weight: bold;
            color: #aaa;
            text-shadow: 1px 1px #fff;
            transition: color 0.3s ease-in-out;
        }

        .toggle-switch input[type="checkbox"]:checked+.toggle-switch-handle {
            transform: translateX(30px);
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2), 0 0 0 3px #F54619;
        }

        .toggle-switch input[type="checkbox"]:checked+.toggle-switch-background {
            background-color: #F54619;
            box-shadow: inset 0 0 0 2px #F54619;
        }

        .toggle-switch input[type="checkbox"]:checked+.toggle-switch:before {
            content: "On";
            color: #F54619;
            right: -15px;
        }

        .toggle-switch input[type="checkbox"]:checked+.toggle-switch-background .toggle-switch-handle {
            transform: translateX(30px);
        }

        /* Compact Date Picker Styles */
        #historyDatePicker {
            color-scheme: light;
            min-width: 120px;
        }

        .dark #historyDatePicker {
            color-scheme: dark;
        }

        #historyDatePicker::-webkit-calendar-picker-indicator {
            cursor: pointer;
            filter: invert(0.5);
            transition: filter 0.2s ease;
            width: 16px;
            height: 16px;
        }

        .dark #historyDatePicker::-webkit-calendar-picker-indicator {
            filter: invert(0.8);
        }

        #historyDatePicker:hover::-webkit-calendar-picker-indicator {
            filter: invert(0.2);
        }

        .dark #historyDatePicker:hover::-webkit-calendar-picker-indicator {
            filter: invert(1);
        }

        /* Compact date picker container */
        .date-picker-container {
            min-height: 44px;
        }

        .date-picker-container:hover {
            box-shadow: 0 2px 8px rgba(245, 70, 25, 0.1);
            border-color: #F54619;
            background: #f8fafc;
        }

        .dark .date-picker-container:hover {
            background: #334155;
            border-color: #F54619;
        }

        /* Compact history controls */
        #historyControls {
            max-width: 100%;
        }

        /* Smooth transitions for all date picker elements */
        .date-picker-container * {
            transition: all 0.2s ease;
        }

        /* Fade in animation for hints */
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animate-fadeIn {
            animation: fadeIn 0.3s ease-out;
        }

        /* Disabled date picker styles */
        #historyDatePicker:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .date-picker-container:has(#historyDatePicker:disabled) {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .date-picker-container:has(#historyDatePicker:disabled):hover {
            border-color: #D5D7DA;
            box-shadow: none;
        }
    </style>
@endpush

<div wire:poll.5s="loadLiveData"
    class="relative flex flex-col-reverse h-auto min-h-[80vh] text-sm md:h-[90%] overflow-y-scroll md:flex-row grow">

    @can('fleet_view')
        @include('partials.fleet-vehicle-list')


        {{-- leaflet map --}}
        <div wire:ignore class="w-full h-full">

            <!-- Leaflet CSS -->


            <div id="map" class="md:min-h-[400px] min-h-screen h-full w-full z-[1]"></div>


            @include('partials.fleet-js')


        </div>

        <script>
            async function getAddressFromCoordinates(lat, lng) {
                let url =
                    `https://**************/nominatim/reverse?lat=${lat}&lon=${lng}&format=json&addressdetails=1&accept-language=it`;

                try {
                    const response = await fetch(url, {
                        headers: {
                            'User-Agent': 'Controllone/1.0 (<EMAIL>)' // Set a custom user-agent
                        }
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }

                    const data = await response.json();

                    if (data && data.display_name) {
                        return data.display_name; // Return the address
                    } else {
                        console.error('No address found for the given coordinates.');
                        return null;
                    }
                } catch (error) {
                    console.error('Error fetching address:', error);
                    return null;
                }
            }
        </script>
        @script
            <script>
                $wire.on('updateMapData', (data) => {
                    setTimeout(() => {
                        vehicleData = data.data;
                        updateMarkers(vehicleData);
                    }, 50);
                });
                $wire.on('update-geofences-on-map', (event) => {
                    updateGeofencesOnMap(event.geofences);
                });
                $wire.on('show-vehicle-route', (event) => {
                    initializeRoute(event.route);
                });
                $wire.on('update-mode', ({
                    liveMode,
                    data
                }) => {
                    clearMapElements();

                    if (liveMode) {
                        historyData = [];

                        if (selectedVehicleLat && selectedVehicleLng) {
                            const latitude = parseFloat(selectedVehicleLat);
                            const longitude = parseFloat(selectedVehicleLng);

                            if (!latitude || !longitude) return;

                            const position = {
                                lat: parseFloat(latitude),
                                lng: parseFloat(longitude)
                            };

                            // Pan and zoom to the position
                            map.panTo(position);
                            map.setZoom(18);
                        }
                    } else {
                        // Handle history mode
                        if (data && data.length > 0) {
                            historyData = data;
                            plotPathOnMap();
                            initializeHistoryMarker(seletedVehicleIcon, 'red');

                            let fullTime = historyData[historyData.length - 1]?.last_update;
                            if (document.getElementById('currentTime')) {
                                document.getElementById('currentTime').innerText = fullTime;
                            }

                            if (document.getElementById('seekbar')) {
                                let seekbar = document.getElementById('seekbar');
                                seekbar.value = (historyData.length - 1);
                            }

                            // Hide loading state
                            showDatePickerLoading(false);
                        }
                    }
                });

                $wire.on('initialize-date-picker', (event) => {
                    setTimeout(() => {
                        initializeDatePicker(event.dates);

                        // Update available dates count
                        const countElement = document.getElementById('availableDatesCount');
                        if (countElement && event.dates) {
                            const count = event.dates.length;
                            if (count === 0) {
                                countElement.textContent = '{{ __("translations.no_historical_data") }}';
                            } else {
                                const dayText = count === 1 ? '{{ __("translations.day_available") }}' : '{{ __("translations.days_available") }}';
                                countElement.textContent = `${count} ${dayText}`;
                            }
                        }
                    }, 100);
                });

                // Listen for selectedDate changes from Livewire
                $wire.on('date-changed', (event) => {
                    const datePicker = document.getElementById('historyDatePicker');
                    if (datePicker && event.date) {
                        const convertedDate = convertDateForInput(event.date);
                        datePicker.value = convertedDate;
                    }

                    // Hide loading state
                    showDatePickerLoading(false);
                });
            </script>
        @endscript
    @endcan
</div>
