<div class="p-4 text-sm">

    <div class="justify-between md:flex">
        <div>
            <h1 class="text-xl font-medium tracking-wide font-poppins text-slate-800 dark:text-slate-100">
                @lang('translations.user_management')
            </h1>
            <p class="mt-2 text-slate-600 dark:text-slate-300">
                @lang('translations.add_edit_assign_roles')
            </p>
        </div>
        <div class="flex justify-end mt-3 h-fit md:mt-0">
            @can('roles_management')
                <a href="{{ route('roles') }}"
                    class="flex items-center justify-center px-5 py-2.5 text-sm tracking-wide text-white transition-colors duration-200 bg-primary rounded-lg shrink-0 sm:w-auto gap-x-2 hover:bg-secondary me-2">
                    <span>@lang('translations.manage_roles')</span>
                </a>
            @endcan

            @can('user_add')
                <button wire:click="addUser"
                    class="flex items-center justify-center px-5 py-2.5 text-sm tracking-wide text-white transition-colors duration-200 bg-primary rounded-lg shrink-0 sm:w-auto gap-x-2 hover:bg-secondary">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                        stroke="currentColor" class="w-5 h-5">
                        <path stroke-linecap="round" stroke-linejoin="round"
                            d="M12 9v6m3-3H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>

                    <span>@lang('translations.add_user')</span>
                </button>
            @endcan

        </div>



    </div>

    @can('user_view')

        <section class="w-full mx-auto mt-4">

            <div class="flex items-center flex-grow gap-4 md:justify-end">

                <div class="w-full max-w-xs">
                    <div class="flex items-center w-full space-x-5">
                        <div
                            class="flex w-full p-3 space-x-2 text-sm bg-gray-100 border border-transparent rounded-lg shadow focus-within:border-gray-300 dark:bg-slate-800 dark:text-slate-300 dark:focus-within:border-slate-500">
                            <svg xmlns="http://www.w3.org/2000/svg" class="flex-shrink-0 w-5 h-5 opacity-30" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                            </svg>
                            <input wire:model.live.debounce.500ms="search" class="bg-transparent outline-none"
                                type="search" placeholder="@lang('translations.search')" />
                        </div>
                    </div>
                </div>

                <div class="w-fit">
                    <select wire:model.live="status" class="bg-transparent outline-none dark:text-slate-200">
                        <option selected>@lang('translations.status')</option>
                        <option value="1">@lang('translations.active')</option>
                        <option value="0">@lang('translations.inactive')</option>
                    </select>
                </div>
            </div>



            <div class="w-full mt-4 mb-20 overflow-hidden bg-white rounded-lg shadow dark:bg-slate-800">

                <div class="w-full mb-5 overflow-x-auto">
                    <table class="w-full">
                        <thead>
                            <tr
                                class="text-xs font-semibold tracking-wide text-left text-gray-600 uppercase bg-gray-100 dark:bg-slate-700 dark:text-slate-300">
                                <th class="px-4 py-3 whitespace-nowrap">@lang('translations.name')</th>
                                <th class="px-4 py-3 whitespace-nowrap">@lang('translations.email')</th>
                                <th class="px-4 py-3 whitespace-nowrap">@lang('translations.role')</th>
                                <th class="px-4 py-3 whitespace-nowrap">@lang('translations.status')</th>
                                <th class="px-4 py-3 whitespace-nowrap">@lang('translations.added_at')</th>
                                <th class="px-4 py-3 whitespace-nowrap text-end md:min-w-28 min-w-40">@lang('translations.actions')
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white dark:bg-slate-800">
                            @forelse ($users as $user)
                                <tr class="text-gray-700 dark:text-slate-300">
                                    <td class="px-4 py-3">
                                        <div>
                                            <p class="text-sm font-semibold text-black dark:text-slate-300">
                                                {{ $user->name ?? '' }}
                                            </p>
                                        </div>
                                    </td>
                                    <td class="px-4 py-3 text-sm font-medium text-primary"><a
                                            href="mailto:{{ $user->email ?? 'N/A' }}">{{ $user->email ?? 'N/A' }}</a>
                                    </td>

                                    <td class="px-4 py-3 text-sm capitalize">
                                        @foreach ($user->roles ?? [] as $role)
                                        <div class="flex flex-wrap gap-2">
                                            <span class="px-2 py-1 rounded-full bg-primary/10 dark:bg-primary/40 w-fit">
                                                {{ $role->name }}
                                            </span>
                                        </div>
                                        @endforeach
                                        </a>
                                    </td>
                                    <td class="px-4 py-3 text-xs">
                                        @if ($user->is_active == 1)
                                            <span
                                                class="px-2 py-1 font-medium leading-tight rounded-full text-emerald-700 bg-emerald-100">
                                                Active </span>
                                        @else
                                            <span
                                                class="px-2 py-1 font-medium leading-tight rounded-full text-rose-700 bg-rose-100">
                                                Inactive </span>
                                        @endif
                                    </td>

                                    <td class="px-4 py-3 text-sm">{{ $user->created_at->format('d-m-Y H:i') }}</td>

                                    <td class="px-4 py-3 text-sm">
                                        <div class="flex items-center justify-end gap-2">

                                            @if ($user->email != '<EMAIL>')
                                                @can('user_vehicle_assignment')
                                                    <div x-data="{ showTooltip: false }" class="relative flex-shrink-0">
                                                        <button wire:click="showAssignedVehicles({{ $user->id }})"
                                                            @mouseenter="showTooltip = true" @mouseleave="showTooltip = false">
                                                            <img class="size-5"
                                                                src="{{ asset('assets/images/icons/steering-wheel.svg') }}"
                                                                alt="Edit">
                                                        </button>
                                                        <div x-show="showTooltip"
                                                            x-transition:enter="transition ease-out duration-200"
                                                            x-transition:enter-start="opacity-0 transform scale-95"
                                                            x-transition:enter-end="opacity-100 transform scale-100"
                                                            x-transition:leave="transition ease-in duration-150"
                                                            x-transition:leave-start="opacity-100 transform scale-100"
                                                            x-transition:leave-end="opacity-0 transform scale-95"
                                                            class="absolute right-0 px-3 py-1 mb-2 text-xs text-white bg-gray-800 rounded shadow-lg bottom-full w-max"
                                                            style="display: none;">
                                                            @lang('translations.asigned_vehicls')
                                                        </div>
                                                    </div>
                                                @endcan
                                            @endif

                                            <div x-data="{ showTooltip: false }" class="relative flex-shrink-0">
                                                <button wire:click="showRecord({{ $user->id }})"
                                                    @mouseenter="showTooltip = true" @mouseleave="showTooltip = false">
                                                    <img class="size-5"
                                                        src="{{ asset('assets/images/icons/eye-icon.svg') }}"
                                                        alt="Edit">
                                                </button>
                                                <div x-show="showTooltip"
                                                    x-transition:enter="transition ease-out duration-200"
                                                    x-transition:enter-start="opacity-0 transform scale-95"
                                                    x-transition:enter-end="opacity-100 transform scale-100"
                                                    x-transition:leave="transition ease-in duration-150"
                                                    x-transition:leave-start="opacity-100 transform scale-100"
                                                    x-transition:leave-end="opacity-0 transform scale-95"
                                                    class="absolute right-0 px-3 py-1 mb-2 text-xs text-white bg-gray-800 rounded shadow-lg bottom-full w-max"
                                                    style="display: none;">
                                                    @lang('translations.view')
                                                </div>
                                            </div>

                                            @can('user_edit')
                                                <!-- Edit Button with Tooltip -->
                                                <div x-data="{ showTooltip: false }" class="relative flex-shrink-0">
                                                    <button wire:click="editRecord({{ $user->id }})"
                                                        @mouseenter="showTooltip = true" @mouseleave="showTooltip = false">
                                                        <img class="size-5" src="{{ asset('assets/images/icons/edit.svg') }}"
                                                            alt="Edit">
                                                    </button>
                                                    <div x-show="showTooltip"
                                                        x-transition:enter="transition ease-out duration-200"
                                                        x-transition:enter-start="opacity-0 transform scale-95"
                                                        x-transition:enter-end="opacity-100 transform scale-100"
                                                        x-transition:leave="transition ease-in duration-150"
                                                        x-transition:leave-start="opacity-100 transform scale-100"
                                                        x-transition:leave-end="opacity-0 transform scale-95"
                                                        class="absolute right-0 px-3 py-1 mb-2 text-xs text-white bg-gray-800 rounded shadow-lg bottom-full w-max"
                                                        style="display: none;">
                                                        @lang('translations.edit')
                                                    </div>
                                                </div>
                                            @endcan

                                            @can('user_delete')
                                                @if ($user->email != '<EMAIL>')
                                                    <!-- Delete Button with Tooltip -->
                                                    <div x-data="{ showTooltip: false }" class="relative flex-shrink-0">
                                                        <button wire:click="deleteRecordConfirmation({{ $user->id }})"
                                                            @mouseenter="showTooltip = true"
                                                            @mouseleave="showTooltip = false">
                                                            <img class="size-5"
                                                                src="{{ asset('assets/images/icons/delete.svg') }}"
                                                                alt="Delete">
                                                        </button>
                                                        <div x-show="showTooltip"
                                                            x-transition:enter="transition ease-out duration-200"
                                                            x-transition:enter-start="opacity-0 transform scale-95"
                                                            x-transition:enter-end="opacity-100 transform scale-100"
                                                            x-transition:leave="transition ease-in duration-150"
                                                            x-transition:leave-start="opacity-100 transform scale-100"
                                                            x-transition:leave-end="opacity-0 transform scale-95"
                                                            class="absolute right-0 px-3 py-1 mb-2 text-xs text-white bg-gray-800 rounded shadow-lg bottom-full w-max"
                                                            style="display: none;">
                                                            @lang('translations.delete')
                                                        </div>
                                                    </div>
                                                @endif
                                            @endcan


                                        </div>

                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="10" class="px-4 py-3 text-center">@lang('translations.no_user_found')
                                    </td>
                                </tr>
                            @endforelse

                        </tbody>
                    </table>
                </div>

                {{ $users->links('livewire.components.pagination') }}

            </div>
        </section>
    @endcan


    {{-- modals --}}
    @canany(['user_add', 'user_edit'])

        <x-modal name="manage-user">
            <x-slot:body>
                <!-- Modal Header -->
                <div class="flex items-center justify-between">

                    <button @click="show = false;" class="ms-auto">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                            stroke="#808080" class="w-6 h-6 cursor-pointer">
                            <path stroke-linecap="round" stroke-linejoin="round"
                                d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </button>
                </div>

                <div class="w-full">

                    <div class="flex items-center justify-center">
                        <h2 class="text-xl font-medium text-center text-black dark:text-slate-100">
                            @if ($recordId)
                                @lang('translations.edit_user')
                            @else
                                @lang('translations.add_user')
                            @endif
                        </h2>
                    </div>


                    <div class="mt-4">

                        <div class="grid grid-cols-2 gap-4">
                            <div class="col-span-2 md:col-span-1">
                                <label class="text-sm text-[#414651] dark:text-slate-200">@lang('translations.name')</label>

                                <div class="relative mt-2">
                                    <input type="text" wire:model="name" id="name"
                                        class="peer py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm rounded-lg text-sm focus:border-primary outline-none transition-all duration-300 dark:text-slate-300 dark:bg-slate-900 dark:border-slate-600 dark:focus:border-primary"
                                        placeholder="@lang('translations.enter') @lang('translations.name')">
                                </div>

                                @error('name')
                                    <div class="mt-2 text-xs text-red-500">
                                        {{ $message }}
                                    </div>
                                @enderror

                            </div>

                            <div class="col-span-2 md:col-span-1">
                                <label class="text-sm text-[#414651] dark:text-slate-200">@lang('translations.email')</label>

                                <div class="relative mt-2">
                                    <input type="email" wire:model="email" id="email"
                                        class="peer py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm rounded-lg text-sm focus:border-primary outline-none transition-all duration-300 dark:text-slate-300 dark:bg-slate-900 dark:border-slate-600 dark:focus:border-primary"
                                        placeholder="@lang('translations.enter') @lang('translations.email')">

                                </div>

                                @error('email')
                                    <div class="mt-2 text-xs text-red-500">
                                        {{ $message }}
                                    </div>
                                @enderror

                            </div>

                            <!-- Password Field -->
                            <div class="col-span-2 md:col-span-1">
                                <label class="text-sm text-[#414651] dark:text-slate-200">@lang('translations.password')</label>
                                <div x-data="{
                                    showPassword: false,
                                    password: '',
                                    generatePassword() {
                                        // Function to generate a random password (8 characters with letters and digits)
                                        const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
                                        let generatedPassword = '';
                                        for (let i = 0; i < 8; i++) {
                                            generatedPassword += chars.charAt(Math.floor(Math.random() * chars.length));
                                        }
                                        this.password = generatedPassword;
                                        // Set the generated password to Livewire model
                                        @this.set('password', generatedPassword);
                                    }
                                }" class="relative mt-2">

                                    <!-- Password Input Field -->
                                    <input x-bind:type="showPassword ? 'text' : 'password'" id="password"
                                        x-model="password" wire:model="password"
                                        placeholder="@lang('translations.enter') password"
                                        class="peer py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm
                               rounded-lg text-sm focus:border-primary outline-none transition-all
                               duration-300 dark:text-slate-300 dark:bg-slate-900 dark:border-slate-600 dark:focus:border-primary">

                                    <!-- Generate Password Button -->
                                    <button type="button" @click="generatePassword"
                                        class="absolute top-0 bottom-0 transition-all duration-300 rounded text-primary right-5">
                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                            stroke-width="1.8" stroke="currentColor" class="size-6">
                                            <path stroke-linecap="round" stroke-linejoin="round"
                                                d="M19.5 12c0-1.232-.046-2.453-.138-3.662a4.006 4.006 0 0 0-3.7-3.7 48.678 48.678 0 0 0-7.324 0 4.006 4.006 0 0 0-3.7 3.7c-.017.22-.032.441-.046.662M19.5 12l3-3m-3 3-3-3m-12 3c0 1.232.046 2.453.138 3.662a4.006 4.006 0 0 0 3.7 3.7 48.656 48.656 0 0 0 7.324 0 4.006 4.006 0 0 0 3.7-3.7c.017-.22.032-.441.046-.662M4.5 12l3 3m-3-3-3 3" />
                                        </svg>
                                    </button>

                                    <!-- password show hide button -->
                                    <div class="absolute z-10 cursor-pointer top-3 right-14"
                                        @click="showPassword = !showPassword">
                                        <img x-show="!showPassword" id="show-icon" class="size-5"
                                            src="{{ asset('assets/images/eye.svg') }}" alt="Show password">
                                        <img x-show="showPassword" id="hide-icon" class="size-5"
                                            src="{{ asset('assets/images/eye-closed.svg') }}" alt="Hide password">
                                    </div>
                                </div>


                                @error('password')
                                    <div class="mt-2 text-xs text-red-500">
                                        {{ $message }}
                                    </div>
                                @enderror
                            </div>



                            @can('add_user_role')
                                <div class="col-span-2">
                                    <label class="text-sm text-[#414651] dark:text-slate-200">@lang('translations.role')</label>
                                    <div class="grid grid-cols-2 gap-1 mt-2">
                                        @foreach ($roles as $role)
                                            <label class="flex items-center gap-2">
                                                <input type="checkbox" value="{{ $role }}" wire:model="userRoles"
                                                    id="role-{{ $role }}" class="accent-primary">
                                                {{ $role }}
                                            </label>
                                        @endforeach

                                    </div>
                                </div>
                            @endcan


                            <div class="flex items-center col-span-2">
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" checked="" wire:model="is_active" id="active"
                                        class="sr-only peer">
                                    <div
                                        class="w-9 h-5 bg-gray-200 hover:bg-gray-300 dark:bg-slate-400 peer-focus:outline-0 peer-focus:ring-transparent rounded-full peer transition-all ease-in-out duration-500 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-primary hover:peer-checked:bg-primary">
                                    </div>
                                </label>

                                <label for="active"
                                    class="ms-2 cursor-pointer select-none text-sm text-[#414651] dark:text-slate-200">
                                    @lang('translations.active')
                                </label>
                            </div>

                        </div>

                        <div class="flex items-center justify-center gap-5 mt-8 text-sm">
                            <button @click="show = false;"
                                class="modal-close text-[#414651] dark:text-slate-400 p-2.5 text-center w-full border-[1.5px] border-[#D5D7DA] rounded-lg shadow-sm">
                                @lang('translations.cancel')
                            </button>

                            <button wire:click="addUpdateUser" wire:loading.attr="disabled" wire:target="addUpdateUser"
                                type="button"
                                class="text-white p-2.5 text-center w-full bg-primary rounded-lg shadow-sm hover:bg-secondary transition-all duration-300">
                                <span wire:loading.remove wire:target="addUpdateUser"> Save
                                </span>
                                <div wire:loading wire:target="addUpdateUser">
                                    <div class="dot-spinner h-[1.4rem!important] w-[1.4rem!important]">
                                        <div class="dot-spinner__dot"></div>
                                        <div class="dot-spinner__dot"></div>
                                        <div class="dot-spinner__dot"></div>
                                        <div class="dot-spinner__dot"></div>
                                        <div class="dot-spinner__dot"></div>
                                        <div class="dot-spinner__dot"></div>
                                        <div class="dot-spinner__dot"></div>
                                        <div class="dot-spinner__dot"></div>
                                    </div>
                                </div>
                            </button>
                        </div>
                    </div>

                </div>
            </x-slot:body>
        </x-modal>
    @endcanany


    @can('user_delete')
        <x-modal name="delete-record-modal">
            <x-slot:body>

                <div class="flex items-center justify-between">
                    <img class="size-10" src="{{ asset('assets/images/icons/delete.svg') }}" alt="delete">

                    <button @click="show = false" class="ms-auto">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                            stroke="#808080" class="w-6 h-6 cursor-pointer">
                            <path stroke-linecap="round" stroke-linejoin="round"
                                d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </button>

                </div>

                <div class="mt-4">
                    <h3 class="font-medium dark:text-slate-100">
                        @lang('translations.confirm_delete_record')
                    </h3>

                    <p class="text-sm mt-2 text-[#535862] dark:text-slate-300">
                        @lang('translations.delete_warning')
                    </p>


                    <div class="flex items-center justify-center gap-5 mt-8 text-sm">
                        <button @click="show = false"
                            class="text-[#414651] p-2.5 text-center w-full border-[1.5px] border-[#D5D7DA] rounded-lg shadow-sm dark:border-slate-400 dark:text-slate-400">
                            @lang('translations.cancel')
                        </button>
                        <button wire:click="deleteRecord"
                            class="text-white p-2.5 text-center w-full bg-red-600 rounded-lg shadow-sm">
                            @lang('translations.delete')
                        </button>
                    </div>
                </div>


            </x-slot:body>
        </x-modal>
    @endcan



    @can('user_view')
        {{-- add client modal --}}
        <x-modal name="show-record">
            <x-slot:body>
                <!-- Modal Header -->
                <div class="flex items-center justify-between">
                    <h2 class="col-span-2 text-lg font-semibold text-center">
                        @lang('translations.user_details')
                    </h2>

                    <button @click="show = false;" class="ms-auto">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                            stroke="#808080" class="w-6 h-6 cursor-pointer">
                            <path stroke-linecap="round" stroke-linejoin="round"
                                d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </button>
                </div>

                <div class="w-full">

                    <div class="mt-4">

                        <div class="grid gap-4 md:grid-cols-2">
                            @if ($selectedRecord)
                                <div class="col-span-2 md:col-span-1">
                                    <label class="text-sm text-gray-600 dark:text-slate-300">@lang('translations.name')</label>
                                    <p class="text-sm text-gray-800 dark:text-gray-100">{{ $selectedRecord->name }}
                                    </p>
                                </div>



                                <div class="col-span-2 md:col-span-1">
                                    <label class="text-sm text-gray-600 dark:text-slate-300">@lang('translations.email')</label>
                                    <p class="text-sm text-primary"><a
                                            href="mailto:{{ $selectedRecord->email }}">{{ $selectedRecord->email }}</a>
                                    </p>
                                </div>
                                <div class="col-span-2 md:col-span-1">
                                    <label class="text-sm text-gray-600 dark:text-slate-300">@lang('translations.role')</label>
                                    <p class="text-sm text-gray-800 capitalize dark:text-gray-100">
                                        {{ $selectedRecord->role }}</p>
                                </div>

                                <div class="col-span-2">
                                    <label class="text-sm text-gray-600 dark:text-slate-300">@lang('translations.role')</label>
                                    <p
                                        class="flex flex-wrap mt-2 text-xs capitalize text-primary dark:text-gray-100 gap-x-2 gap-y-3">
                                        @foreach ($selectedRecord->roles ?? [] as $role)
                                            <span class="px-2 py-1 rounded-full bg-primary/10 dark:bg-primary/40 w-fit">
                                                {{ $role->name }}
                                            </span>
                                        @endforeach
                                    </p>
                                </div>


                                <div class="col-span-2 md:col-span-1">
                                    <label class="text-sm text-gray-600 dark:text-slate-300">@lang('translations.created_at')</label>
                                    <p class="text-sm text-gray-800 dark:text-gray-100">
                                        {{ $selectedRecord->created_at->format('d-m-Y H:i') }}</p>
                                </div>

                                <div class="col-span-2">
                                    <label class="text-sm text-gray-600 dark:text-slate-300">@lang('translations.active')</label>
                                    <p class="mt-3 text-sm text-gray-800 dark:text-gray-100">
                                        @if ($selectedRecord->is_active == 1)
                                            <span
                                                class="px-2 py-1 font-medium leading-tight rounded-full text-emerald-700 bg-emerald-100">
                                                Active </span>
                                        @else
                                            <span
                                                class="px-2 py-1 font-medium leading-tight rounded-full text-rose-700 bg-rose-100">
                                                Inactive </span>
                                        @endif
                                    </p>
                                </div>
                            @else
                                <p>@lang('translations.no_user_found')</p>
                            @endif
                        </div>


                    </div>

                </div>
            </x-slot:body>
        </x-modal>
    @endcan


    <x-modal name="user-vehicles">
        <x-slot:body>
            <!-- Modal Header -->
            <div class="w-full">

                <!-- Modal Header -->
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-lg font-semibold">@lang('translations.user_vehicles')</h2>
                    <button @click="show = false;" class="ms-auto">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                            stroke-width="1.5" stroke="#808080" class="w-6 h-6 cursor-pointer">
                            <path stroke-linecap="round" stroke-linejoin="round"
                                d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </button>
                </div>

                <div class="w-full">
                    <div class="flex items-center justify-end">
                        <button wire:click="addUserVehicle" type="button"
                            class="text-white p-2.5 px-4 text-center w-fit ms-auto bg-primary rounded-lg shadow-sm hover:bg-secondary transition-all duration-300">
                            @lang('translations.add_vehicle_user')
                        </button>
                    </div>
                    @if ($userVehicles)
                        <div class="mt-2 overflow-x-auto">
                            <table
                                class="min-w-full text-sm bg-white dark:bg-slate-900 dark:text-slate-100 text-slate-800">
                                <thead>
                                    <tr>
                                        <th
                                            class="px-4 py-2 text-xs leading-4 tracking-wider text-left text-gray-600 uppercase bg-gray-100 border-b border-gray-200 dark:text-slate-200 dark:bg-slate-700 whitespace-nowrap">
                                            @lang('translations.vehicle')</th>
                                        <th
                                            class="px-4 py-2 text-xs leading-4 tracking-wider text-left text-gray-600 uppercase bg-gray-100 border-b border-gray-200 dark:text-slate-200 dark:bg-slate-700 whitespace-nowrap">
                                            @lang('translations.type')</th>
                                        <th
                                            class="px-4 py-2 text-xs leading-4 tracking-wider text-left text-gray-600 uppercase bg-gray-100 border-b border-gray-200 dark:text-slate-200 dark:bg-slate-700 whitespace-nowrap">
                                            @lang('translations.model')</th>
                                        <th
                                            class="px-4 py-2 text-xs leading-4 tracking-wider text-left text-gray-600 uppercase bg-gray-100 border-b border-gray-200 dark:text-slate-200 dark:bg-slate-700 whitespace-nowrap">
                                            @lang('translations.assigned_at')</th>
                                        <th
                                            class="px-4 py-2 text-xs leading-4 tracking-wider text-gray-600 uppercase bg-gray-100 border-b border-gray-200 dark:text-slate-200 dark:bg-slate-700 whitespace-nowrap text-end">
                                            @lang('translations.actions')</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse ($userVehicles ?? [] as $userVehicle)
                                        <tr>
                                            <td class="px-4 py-2 border-b border-gray-200">
                                                {{ $userVehicle->vehicle?->license_plate ?? 'N/A' }}
                                            </td>
                                            <td class="px-4 py-2 border-b border-gray-200">
                                                {{ $userVehicle->vehicle?->model ?? 'N/A' }}
                                            </td>
                                            <td class="px-4 py-2 border-b border-gray-200">
                                                <div class="flex items-center gap-2 ">

                                                    @lang('translations.' . strtolower($userVehicle->vehicle?->type))

                                                    <img class="size-4"
                                                        src="{{ asset('assets/images/icons/' . ($userVehicle->vehicle?->icon ?? 'default') . '.svg') }}"
                                                        alt="icon">
                                                </div>
                                            </td>
                                            <td class="px-4 py-2 border-b border-gray-200">
                                                {{ $userVehicle->created_at->format('d/m/Y H:i') ?? 'N/A' }}
                                            </td>
                                            <td class="px-4 py-2 border-b border-gray-200 ">

                                                <div class="flex items-center justify-end gap-1">
                                                    <!-- Delete Button with Tooltip -->
                                                    <div x-data="{ showTooltip: false }" class="relative flex-shrink-0">
                                                        <button
                                                            wire:click="deleteUserVehicleConfirmation({{ $userVehicle->id }})"
                                                            @mouseenter="showTooltip = true"
                                                            @mouseleave="showTooltip = false">
                                                            <img class="size-5"
                                                                src="{{ asset('assets/images/icons/delete.svg') }}"
                                                                alt="Delete">
                                                        </button>
                                                        <div x-show="showTooltip"
                                                            x-transition:enter="transition ease-out duration-200"
                                                            x-transition:enter-start="opacity-0 transform scale-95"
                                                            x-transition:enter-end="opacity-100 transform scale-100"
                                                            x-transition:leave="transition ease-in duration-150"
                                                            x-transition:leave-start="opacity-100 transform scale-100"
                                                            x-transition:leave-end="opacity-0 transform scale-95"
                                                            class="absolute right-0 px-3 py-1 mb-2 text-xs text-white bg-gray-800 rounded shadow-lg bottom-full w-max"
                                                            style="display: none;">
                                                            @lang('translations.delete')
                                                        </div>
                                                    </div>
                                                </div>

                                            </td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td colspan="4" class="p-3 text-center">
                                                @lang('translations.no_user_found')
                                            </td>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    @else
                        <p class="text-center">@lang('translations.no_user_found')</p>
                    @endif


                </div>

            </div>
        </x-slot:body>
    </x-modal>


    <x-modal name="add-user-vehicle">
        <x-slot:body>
            <!-- Modal Header -->
            <div class="w-full">

                <!-- Modal Header -->
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-lg font-semibold">@lang('translations.add_vehicle_user')</h2>
                    <button @click="show = false;" class="ms-auto">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                            stroke-width="1.5" stroke="#808080" class="w-6 h-6 cursor-pointer">
                            <path stroke-linecap="round" stroke-linejoin="round"
                                d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </button>
                </div>

                <div class="w-full">
                    <div class="col-span-2">
                        <label class="text-sm text-[#414651] dark:text-slate-300">@lang('translations.select_vehicles')</label>


                        <livewire:components.select-dropdown :placeholder="__('translations.search_vehicle')" field-name="userVehicle"
                            fetch-method="getVehicles" />

                        @error('userVehicle')
                            <div class="mt-2 text-xs text-red-500">
                                {{ $message }}
                            </div>
                        @enderror
                    </div>

                </div>

                <div class="flex items-center justify-center gap-5 mt-8 text-sm">
                    <button @click="show = false;"
                        class="modal-close text-[#414651] dark:border-slate-600 dark:text-slate-500 p-2.5 text-center w-full border-[1.5px] border-[#D5D7DA] rounded-lg shadow-sm">
                        @lang('translations.cancel')
                    </button>

                    <button wire:click="assignUserVehicle" wire:loading.attr="disabled"
                        wire:target="assignUserVehicle" type="button"
                        class="text-white p-2.5 text-center w-full bg-primary rounded-lg shadow-sm hover:bg-primaryDark transition-all duration-300">
                        <span wire:loading.remove wire:target="assignUserVehicle"> @lang('translations.save')
                        </span>
                        <div wire:loading wire:target="assignUserVehicle">
                            <div class="dot-spinner h-[1.4rem!important] w-[1.4rem!important]">
                                <div class="dot-spinner__dot"></div>
                                <div class="dot-spinner__dot"></div>
                                <div class="dot-spinner__dot"></div>
                                <div class="dot-spinner__dot"></div>
                                <div class="dot-spinner__dot"></div>
                                <div class="dot-spinner__dot"></div>
                                <div class="dot-spinner__dot"></div>
                                <div class="dot-spinner__dot"></div>
                            </div>
                        </div>
                    </button>
                </div>

            </div>
        </x-slot:body>
    </x-modal>

    <x-modal name="delete-user-vehicle-record-modal">
        <x-slot:body>

            <div class="flex items-center justify-between">
                <img class="size-10" src="{{ asset('assets/images/icons/delete.svg') }}" alt="delete">

                <button @click="show = false" class="ms-auto">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                        stroke="#808080" class="w-6 h-6 cursor-pointer">
                        <path stroke-linecap="round" stroke-linejoin="round"
                            d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </button>

            </div>

            <div class="mt-4">
                <h3 class="font-medium dark:text-slate-200">
                    @lang('translations.confirm_delete_record')
                </h3>

                <p class="text-sm mt-2 text-[#535862]da rk:text-slate-300 ">
                    @lang('translations.delete_warning')
                </p>


                <div class="flex items-center justify-center gap-5 mt-8 text-sm">
                    <button @click="show = false"
                        class="text-[#414651] p-2.5 text-center w-full border-[1.5px] border-[#D5D7DA] rounded-lg shadow-sm dark:text-slate-400 dark:border-slate-600">
                        @lang('translations.cancel')
                    </button>
                    <button wire:click="deleteVehicleUser"
                        class="text-white p-2.5 text-center w-full bg-red-600 rounded-lg shadow-sm">
                        @lang('translations.delete')
                    </button>
                </div>
            </div>


        </x-slot:body>
    </x-modal>


</div>
