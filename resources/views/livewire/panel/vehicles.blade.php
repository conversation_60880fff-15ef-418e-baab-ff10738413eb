<div class="p-4 text-sm">

    <div class="justify-between md:flex">
        <div>
            <h1 class="text-xl font-medium tracking-wide font-poppins text-slate-800 dark:text-slate-100">
                @lang('translations.vehicle_management')
            </h1>
            <p class="mt-2 text-slate-600 dark:text-slate-300">
                @lang('translations.maintain_track_records')
            </p>
        </div>
        <div class="flex items-center justify-between gap-3 md:justify-end">
            @can('driver_view')
                <div class="flex mt-3 h-fit md:mt-0">
                    <a href="{{ route('drivers') }}"
                        class="flex items-center justify-center px-5 py-2.5 text-sm tracking-wide hover:text-white transition-all duration-200 bg-transparent rounded-lg shrink-0 sm:w-auto gap-x-2 hover:bg-primary text-primary border-primary border">
                        <span>@lang('translations.manage_drivers')</span>
                    </a>
                </div>
            @endcan

            @can('vehicle_add')
                <div class="flex mt-3 h-fit md:mt-0">
                    <button wire:click="addVehicle"
                        class="flex items-center justify-center px-5 py-2.5 text-sm tracking-wide text-white transition-colors duration-200 bg-primary rounded-lg shrink-0 sm:w-auto gap-x-2 hover:bg-primaryDark ">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                            stroke="currentColor" class="w-5 h-5">
                            <path stroke-linecap="round" stroke-linejoin="round"
                                d="M12 9v6m3-3H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>

                        <span>@lang('translations.add_vehicle')</span>
                    </button>
                </div>
            @endcan


        </div>


    </div>


    @can('vehicle_view')

        <section class="w-full mx-auto mt-4 ">

            <div class="flex items-center flex-grow gap-4 md:justify-end">

                <div class="w-full max-w-xs">
                    <div class="flex items-center w-full space-x-5">
                        <div
                            class="flex w-full p-3 space-x-2 text-sm bg-gray-100 border border-transparent rounded-lg shadow dark:bg-slate-800 focus-within:border-gray-300 dark:focus-within:border-slate-600 dark:text-slate-300">
                            <svg xmlns="http://www.w3.org/2000/svg" class="flex-shrink-0 w-5 h-5 opacity-30" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                            </svg>
                            <input wire:model.live.debounce.500ms="search" class="bg-transparent outline-none"
                                type="search" placeholder="Search" />
                        </div>
                    </div>
                </div>

                <div class="w-fit">
                    <select wire:model.live="filter_status" class="bg-transparent outline-none dark:text-slate-200">
                        <option selected>@lang('translations.status')</option>
                        <option value="1">@lang('translations.active')</option>
                        <option value="0">@lang('translations.inactive')</option>
                    </select>
                </div>
            </div>



            <div class="w-full mt-4 mb-20 overflow-hidden bg-white rounded-lg shadow dark:bg-slate-800">

                <div class="w-full mb-5 overflow-x-auto">
                    <table class="w-full">
                        <thead>
                            <tr
                                class="text-xs font-semibold tracking-wide text-left text-gray-600 uppercase bg-gray-100 dark:bg-slate-900 dark:text-slate-100">
                                <th class="px-4 py-3 whitespace-nowrap">@lang('translations.license_plate')</th>
                                <th class="px-4 py-3 whitespace-nowrap">@lang('translations.driver')</th>
                                <th class="px-4 py-3 whitespace-nowrap">@lang('translations.device_imei')</th>
                                <th class="px-4 py-3 whitespace-nowrap">@lang('translations.model')</th>
                                <th class="px-4 py-3 whitespace-nowrap">@lang('translations.type')</th>
                                <th class="px-4 py-3 whitespace-nowrap">@lang('translations.icon')</th>
                                <th class="px-4 py-3 whitespace-nowrap">@lang('translations.status')</th>
                                <th class="px-4 py-3 whitespace-nowrap text-end md:min-w-20 min-w-48">@lang('translations.actions')
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white dark:bg-slate-800">
                            @forelse ($vehicles as $vehicle)
                                <tr class="text-gray-700 dark:text-slate-300">
                                    <td class="px-4 py-3">
                                        <div class="relative w-fit">
                                            <p class="text-sm font-semibold text-black dark:text-slate-300">
                                                {{ $vehicle->license_plate ?? '' }}
                                            </p>

                                            @php
                                                $maintenanceEvent = $vehicle->events->first();
                                                $iconClass = '';
                                                $message = '';
                                                if ($maintenanceEvent) {
                                                    if ($maintenanceEvent->maintenance_status === 'pending') {
                                                        $iconClass = 'text-yellow-500';
                                                        $message = __('translations.vehicle_under_maintenance');
                                                    } elseif ($maintenanceEvent->maintenance_status === 'in progress') {
                                                        $iconClass = 'text-blue-500';
                                                        $message = __('translations.vehicle_maintenance_in_progress');
                                                    }
                                                }
                                            @endphp

                                            {{-- Show Icon if Maintenance Event Exists --}}
                                            @if ($iconClass)
                                                <div x-data="{ showTooltip: false }"
                                                    class="absolute -top-3 -right-5 {{ $iconClass }} rounded-full w-fit"
                                                    @mouseenter="showTooltip = true" @mouseleave="showTooltip = false">
                                                    <svg wire:ignore xmlns="http://www.w3.org/2000/svg" fill="none"
                                                        viewBox="0 0 24 24" stroke-width="1.8" stroke="currentColor"
                                                        class="size-4">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            d="M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                                                    </svg>

                                                    <div class="relative">
                                                        <div x-show="showTooltip" x-cloak
                                                            x-transition:enter="transition ease-out duration-200"
                                                            x-transition:enter-start="opacity-0 transform scale-95"
                                                            x-transition:enter-end="opacity-100 transform scale-100"
                                                            x-transition:leave="transition ease-in duration-150"
                                                            x-transition:leave-start="opacity-100 transform scale-100"
                                                            x-transition:leave-end="opacity-0 transform scale-95"
                                                            class="absolute left-0 px-3 py-1 mb-2 text-xs text-center text-white bg-gray-800 rounded shadow-lg bottom-full w-max">
                                                            {{ $message }}
                                                        </div>
                                                    </div>

                                                </div>
                                            @endif
                                        </div>
                                    </td>
                                    <td class="px-4 py-3 text-sm font-medium">
                                        {{ $vehicle->driver?->name ?? 'N/A' }}
                                    </td>
                                    <td class="px-4 py-3 text-sm font-medium">
                                        {{ $vehicle->imei ?? 'N/A' }}
                                    </td>
                                    <td class="px-4 py-3 text-sm font-medium">
                                        {{ $vehicle->model ?? 'N/A' }}
                                    </td>

                                    <td class="px-4 py-3 text-sm capitalize">
                                        @lang('translations.' . ($vehicle->type ?? 'other'))
                                    </td>

                                    <td class="flex items-center gap-2 px-4 py-3 text-sm capitalize">
                                        <img class="size-6"
                                            src="{{ asset('assets/images/icons/' . ($vehicle->icon ?? 'default') . '.svg') }}"
                                            alt="Car">
                                    </td>

                                    <td class="px-4 py-3 text-xs">
                                        @if ($vehicle->status == 1)
                                            <span
                                                class="px-2 py-1 font-medium leading-tight rounded-full text-emerald-700 bg-emerald-100">
                                                @lang('translations.active') </span>
                                        @else
                                            <span
                                                class="px-2 py-1 font-medium leading-tight rounded-full text-rose-700 bg-rose-100">
                                                @lang('translations.inactive') </span>
                                        @endif
                                    </td>


                                    <td class="px-4 py-3 text-sm">
                                        <div class="flex items-center justify-end gap-2">

                                            @can('vehicle_route_management')
                                                <div x-data="{ showTooltip: false }" class="relative flex-shrink-0">
                                                    <a href="{{ route('vehicle-routes', ['vehicleId' => $vehicle->id]) }}"
                                                        @mouseenter="showTooltip = true" @mouseleave="showTooltip = false">
                                                        <img class="size-5" src="{{ asset('assets/images/icons/route.svg') }}"
                                                            alt="Edit">
                                                    </a>
                                                    <div x-show="showTooltip"
                                                        x-transition:enter="transition ease-out duration-200"
                                                        x-transition:enter-start="opacity-0 transform scale-95"
                                                        x-transition:enter-end="opacity-100 transform scale-100"
                                                        x-transition:leave="transition ease-in duration-150"
                                                        x-transition:leave-start="opacity-100 transform scale-100"
                                                        x-transition:leave-end="opacity-0 transform scale-95"
                                                        class="absolute right-0 px-3 py-1 mb-2 text-xs text-white bg-gray-800 rounded shadow-lg bottom-full w-max"
                                                        style="display: none;">
                                                        @lang('translations.vehicle_routes')
                                                    </div>
                                                </div>
                                            @endcan

                                            @can('vehicle_users')
                                                <div x-data="{ showTooltip: false }" class="relative flex-shrink-0">
                                                    <button wire:click="showVehicleUsers({{ $vehicle->id }})"
                                                        @mouseenter="showTooltip = true" @mouseleave="showTooltip = false">
                                                        <img class="size-6"
                                                            src="{{ asset('assets/images/icons/users.svg') }}"
                                                            alt="Edit">
                                                    </button>
                                                    <div x-show="showTooltip"
                                                        x-transition:enter="transition ease-out duration-200"
                                                        x-transition:enter-start="opacity-0 transform scale-95"
                                                        x-transition:enter-end="opacity-100 transform scale-100"
                                                        x-transition:leave="transition ease-in duration-150"
                                                        x-transition:leave-start="opacity-100 transform scale-100"
                                                        x-transition:leave-end="opacity-0 transform scale-95"
                                                        class="absolute right-0 px-3 py-1 mb-2 text-xs text-white bg-gray-800 rounded shadow-lg bottom-full w-max"
                                                        style="display: none;">
                                                        @lang('translations.vehicle_users')
                                                    </div>
                                                </div>
                                            @endcan

                                            @can('vehicle_events_view')
                                                <div x-data="{ showTooltip: false }" class="relative flex-shrink-0">
                                                    <button wire:click="showCalendar({{ $vehicle->id }})"
                                                        @mouseenter="showTooltip = true" @mouseleave="showTooltip = false">
                                                        <img class="size-5"
                                                            src="{{ asset('assets/images/icons/calender.svg') }}"
                                                            alt="Edit">
                                                    </button>
                                                    <div x-show="showTooltip"
                                                        x-transition:enter="transition ease-out duration-200"
                                                        x-transition:enter-start="opacity-0 transform scale-95"
                                                        x-transition:enter-end="opacity-100 transform scale-100"
                                                        x-transition:leave="transition ease-in duration-150"
                                                        x-transition:leave-start="opacity-100 transform scale-100"
                                                        x-transition:leave-end="opacity-0 transform scale-95"
                                                        class="absolute right-0 px-3 py-1 mb-2 text-xs text-white bg-gray-800 rounded shadow-lg bottom-full w-max"
                                                        style="display: none;">
                                                        @lang('translations.calendar')
                                                    </div>
                                                </div>
                                            @endcan

                                            <div x-data="{ showTooltip: false }" class="relative flex-shrink-0">
                                                <button wire:click="showRecord({{ $vehicle->id }})"
                                                    @mouseenter="showTooltip = true" @mouseleave="showTooltip = false">
                                                    <img class="size-5"
                                                        src="{{ asset('assets/images/icons/eye-icon.svg') }}"
                                                        alt="Edit">
                                                </button>
                                                <div x-show="showTooltip"
                                                    x-transition:enter="transition ease-out duration-200"
                                                    x-transition:enter-start="opacity-0 transform scale-95"
                                                    x-transition:enter-end="opacity-100 transform scale-100"
                                                    x-transition:leave="transition ease-in duration-150"
                                                    x-transition:leave-start="opacity-100 transform scale-100"
                                                    x-transition:leave-end="opacity-0 transform scale-95"
                                                    class="absolute right-0 px-3 py-1 mb-2 text-xs text-white bg-gray-800 rounded shadow-lg bottom-full w-max"
                                                    style="display: none;">
                                                    @lang('translations.view')
                                                </div>
                                            </div>

                                            @can('vehicle_edit')
                                                <!-- Edit Button with Tooltip -->
                                                <div x-data="{ showTooltip: false }" class="relative flex-shrink-0">
                                                    <button wire:click="editRecord({{ $vehicle->id }})"
                                                        @mouseenter="showTooltip = true" @mouseleave="showTooltip = false">
                                                        <img class="size-5" src="{{ asset('assets/images/icons/edit.svg') }}"
                                                            alt="Edit">
                                                    </button>
                                                    <div x-show="showTooltip"
                                                        x-transition:enter="transition ease-out duration-200"
                                                        x-transition:enter-start="opacity-0 transform scale-95"
                                                        x-transition:enter-end="opacity-100 transform scale-100"
                                                        x-transition:leave="transition ease-in duration-150"
                                                        x-transition:leave-start="opacity-100 transform scale-100"
                                                        x-transition:leave-end="opacity-0 transform scale-95"
                                                        class="absolute right-0 px-3 py-1 mb-2 text-xs text-white bg-gray-800 rounded shadow-lg bottom-full w-max"
                                                        style="display: none;">
                                                        @lang('translations.edit')
                                                    </div>
                                                </div>
                                            @endcan

                                            @can('vehicle_delete')
                                                <!-- Delete Button with Tooltip -->
                                                <div x-data="{ showTooltip: false }" class="relative flex-shrink-0">
                                                    <button wire:click="deleteRecordConfirmation({{ $vehicle->id }})"
                                                        @mouseenter="showTooltip = true" @mouseleave="showTooltip = false">
                                                        <img class="size-5"
                                                            src="{{ asset('assets/images/icons/delete.svg') }}"
                                                            alt="Delete">
                                                    </button>
                                                    <div x-show="showTooltip"
                                                        x-transition:enter="transition ease-out duration-200"
                                                        x-transition:enter-start="opacity-0 transform scale-95"
                                                        x-transition:enter-end="opacity-100 transform scale-100"
                                                        x-transition:leave="transition ease-in duration-150"
                                                        x-transition:leave-start="opacity-100 transform scale-100"
                                                        x-transition:leave-end="opacity-0 transform scale-95"
                                                        class="absolute right-0 px-3 py-1 mb-2 text-xs text-white bg-gray-800 rounded shadow-lg bottom-full w-max"
                                                        style="display: none;">
                                                        @lang('translations.delete')
                                                    </div>
                                                </div>
                                            @endcan


                                        </div>

                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="10" class="px-4 py-3 text-center dark:text-slate-300">
                                        @lang('translations.no_vehicle_found')
                                    </td>
                                </tr>
                            @endforelse

                        </tbody>
                    </table>
                </div>

                {{ $vehicles->links('livewire.components.pagination') }}

            </div>
        </section>
    @endcan

    {{-- modals --}}

    @canany(['vehicle_add', 'vehicle_edit'])
        <x-modal name="manage-vehicle">
            <x-slot:body>
                <!-- Modal Header -->
                <div class="flex items-center justify-between">

                    <button @click="show = false;" class="ms-auto">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                            stroke="#808080" class="w-6 h-6 cursor-pointer">
                            <path stroke-linecap="round" stroke-linejoin="round"
                                d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </button>
                </div>

                <div class="w-full">

                    <div class="flex items-center justify-center">
                        <h2 class="text-xl font-medium text-center text-black dark:text-slate-100">
                            @if ($recordId)
                                @lang('translations.edit_vehicle')
                            @else
                                @lang('translations.add_vehicle')
                            @endif
                        </h2>
                    </div>


                    <div class="mt-4">

                        <div class="grid grid-cols-2 gap-4">
                            <div class="col-span-2 md:col-span-1">
                                <label class="text-sm text-[#414651] dark:text-slate-300">@lang('translations.device_imei')</label>

                                <div class="relative mt-2">
                                    <input type="text"
                                        @if ($user && $user->role != 'admin') disabled style="opacity: 0.5;" @endif
                                        wire:model="imei" id="imei"
                                        class="peer py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm rounded-lg text-sm focus:border-primary outline-none transition-all duration-300 dark:text-slate-300 dark:bg-slate-800 dark:border-slate-600 dark:focus:border-primary"
                                        placeholder="@lang('translations.enter') @lang('translations.imei')">
                                </div>

                                @error('imei')
                                    <div class="mt-2 text-xs text-red-500">
                                        {{ $message }}
                                    </div>
                                @enderror

                            </div>
                            <div class="col-span-2 md:col-span-1">
                                <label class="text-sm text-[#414651] dark:text-slate-300">@lang('translations.license_plate')</label>

                                <div class="relative mt-2">
                                    <input type="text" wire:model="license_plate" id="license_plate"
                                        class="peer py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm rounded-lg text-sm focus:border-primary outline-none transition-all duration-300 dark:text-slate-300 dark:bg-slate-800 dark:border-slate-600 dark:focus:border-primary"
                                        placeholder="@lang('translations.enter') @lang('translations.license_plate')">
                                </div>

                                @error('license_plate')
                                    <div class="mt-2 text-xs text-red-500">
                                        {{ $message }}
                                    </div>
                                @enderror

                            </div>

                            <div class="col-span-2 md:col-span-1">
                                <label class="text-sm text-[#414651] dark:text-slate-300">@lang('translations.model')</label>
                                <div class="relative mt-2">
                                    <input type="text" wire:model="model" id="model"
                                        class="peer py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm rounded-lg text-sm focus:border-primary outline-none transition-all duration-300 dark:text-slate-300 dark:bg-slate-800 dark:border-slate-600 dark:focus:border-primary"
                                        placeholder="@lang('translations.enter') @lang('translations.model')">
                                </div>
                                @error('model')
                                    <div class="mt-2 text-xs text-red-500">
                                        {{ $message }}
                                    </div>
                                @enderror
                            </div>




                            <div class="col-span-2 md:col-span-1">
                                <label class="text-sm text-[#414651] dark:text-slate-300">@lang('translations.year_of_registration')</label>
                                <div class="relative mt-2">
                                    <input type="date" wire:model="year_of_registration" id="year_of_registration"
                                        class="peer py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm rounded-lg text-sm focus:border-primary outline-none transition-all duration-300 dark:text-slate-300 dark:bg-slate-800 dark:border-slate-600 dark:focus:border-primary"
                                        placeholder="@lang('translations.enter') @lang('translations.year_of_registration')">
                                </div>
                                @error('year_of_registration')
                                    <div class="mt-2 text-xs text-red-500">
                                        {{ $message }}
                                    </div>
                                @enderror
                            </div>


                            <div class="col-span-2 md:col-span-1">
                                <label class="text-sm text-[#414651] dark:text-slate-300">VIN</label>
                                <div class="relative mt-2">
                                    <input type="text" wire:model="vin" id="vin"
                                        class="peer py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm rounded-lg text-sm focus:border-primary outline-none transition-all duration-300 dark:text-slate-300 dark:bg-slate-800 dark:border-slate-600 dark:focus:border-primary"
                                        placeholder="@lang('translations.enter') VIN">
                                </div>
                                @error('vin')
                                    <div class="mt-2 text-xs text-red-500">
                                        {{ $message }}
                                    </div>
                                @enderror
                            </div>
                            <div class="col-span-2 md:col-span-1">
                                <label class="text-sm text-[#414651] dark:text-slate-300">@lang('translations.mileage')</label>
                                <div class="relative mt-2">
                                    <input type="text" wire:model="mileage" id="mileage"
                                        class="peer py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm rounded-lg text-sm focus:border-primary outline-none transition-all duration-300 dark:text-slate-300 dark:bg-slate-800 dark:border-slate-600 dark:focus:border-primary"
                                        placeholder="@lang('translations.enter') @lang('translations.mileage')">
                                </div>
                                @error('mileage')
                                    <div class="mt-2 text-xs text-red-500">
                                        {{ $message }}
                                    </div>
                                @enderror
                            </div>

                            <div class="col-span-2">
                                <label class="text-sm text-[#414651] dark:text-slate-300">@lang('translations.current_odometer_reading') (km)</label>
                                <div class="relative mt-2">
                                    <input type="text" wire:model="current_odometer_reading"
                                        id="current_odometer_reading"
                                        class="peer py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm rounded-lg text-sm focus:border-primary outline-none transition-all duration-300 dark:text-slate-300 dark:bg-slate-800 dark:border-slate-600 dark:focus:border-primary"
                                        placeholder="@lang('translations.enter') @lang('translations.current_odometer_reading')">
                                </div>
                                @error('current_odometer_reading')
                                    <div class="mt-2 text-xs text-red-500">
                                        {{ $message }}
                                    </div>
                                @enderror
                            </div>


                            <div class="w-full col-span-2 md:col-span-1">
                                <label class="text-sm text-[#414651] dark:text-slate-300">@lang('translations.type')</label>

                                <div class="relative mt-2">
                                    <select wire:model="type" id="type"
                                        class="peer py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm rounded-lg text-sm focus:border-primary outline-none transition-all duration-300 dark:text-slate-300 dark:bg-slate-800 dark:border-slate-600 dark:focus:border-primary">
                                        <option value="">@lang('translations.select')</option>
                                        <option value="car">@lang('translations.car')</option>
                                        <option value="truck">@lang('translations.truck')</option>
                                        <option value="bus">@lang('translations.bus')</option>
                                        <option value="bike">@lang('translations.bike')</option>
                                        <option value="cycle">@lang('translations.cycle')</option>
                                        <option value="scooter">@lang('translations.scooter')</option>
                                        <option value="van">@lang('translations.van')</option>
                                        <option value="tractor">@lang('translations.tractor')</option>
                                        <option value="ambulance">@lang('translations.ambulance')</option>
                                        <option value="boat">@lang('translations.boat')</option>
                                        <option value="other">@lang('translations.other')</option>
                                    </select>

                                </div>

                                @error('type')
                                    <div class="mt-2 text-xs text-red-500">
                                        {{ $message }}
                                    </div>
                                @enderror

                            </div>

                            <div x-data="{ open: false, selectedIcon: @entangle('icon') }" class="relative w-full col-span-2 md:col-span-1">
                                <label class="text-sm text-[#414651] dark:text-slate-300">@lang('translations.icon')</label>

                                <!-- Button to Open Dropdown -->
                                <button @click="open = !open"
                                    class="flex items-center justify-between gap-2 px-3 py-2.5 mt-2 text-sm transition-all duration-300 bg-white border-[1.5px] border-gray-300 rounded-lg shadow-md outline-none w-full peer focus:border-primary dark:text-slate-300 dark:bg-slate-800 dark:border-slate-600 dark:focus:border-primary">
                                    <div class="flex items-center">
                                        <template x-if="selectedIcon">
                                            <img :src="'{{ asset('assets/images/icons') }}/' + selectedIcon + '.svg'"
                                                alt="Selected Icon" class="w-6 h-6 me-2" />
                                        </template>
                                        <span>Icon</span>
                                    </div>
                                    <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 text-gray-500" fill="none"
                                        viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M19 9l-7 7-7-7" />
                                    </svg>
                                </button>

                                <!-- Dropdown Menu -->
                                <div x-show="open" @click.away="open = false" x-transition
                                    class="absolute z-10 mt-2 bg-white border border-gray-300 rounded shadow-lg w-fit dark:bg-slate-800 dark:border-slate-500">
                                    <ul class="overflow-y-auto max-h-48">
                                        @foreach (['car', 'bike', 'truck', 'bus', 'van', 'suv', 'cycle', 'scooter', 'taxi', 'ambulance', 'tractor', 'boat', 'default', 'anomaly'] as $icon)
                                            <li @click="selectedIcon = '{{ $icon }}'; $wire.updateSelectedIcon(selectedIcon); open = false"
                                                class="flex items-center px-4 py-2 cursor-pointer hover:bg-gray-100 dark:hover:bg-slate-900">
                                                <img src="{{ asset('assets/images/icons/' . $icon . '.svg') }}"
                                                    alt="{{ $icon }}" class="w-6 h-6 mr-2" />
                                            </li>
                                        @endforeach
                                    </ul>
                                </div>
                            </div>



                            <div class="col-span-2">
                                <label class="text-sm text-[#414651] dark:text-slate-300">@lang('translations.driver')</label>


                                <livewire:components.select-dropdown placeholder="{{ __('translations.select_driver') }}"
                                    field-name="selectedDriver" fetch-method="getDrivers" />

                                @error('driver')
                                    <div class="mt-2 text-xs text-red-500">
                                        {{ $message }}
                                    </div>
                                @enderror

                            </div>


                            <div class="flex items-center col-span-2">
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" checked="" wire:model="status" id="active"
                                        class="sr-only peer">
                                    <div
                                        class="w-9 h-5 bg-gray-200 hover:bg-gray-300 peer-focus:outline-0 peer-focus:ring-transparent rounded-full peer transition-all ease-in-out duration-500 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-primary hover:peer-checked:bg-primary">
                                    </div>
                                </label>

                                <label for="active"
                                    class="ms-2 cursor-pointer select-none text-sm text-[#414651] dark:text-slate-300">
                                    @lang('translations.active')
                                </label>
                            </div>
                        </div>

                        <div class="flex items-center justify-center gap-5 mt-8 text-sm">
                            <button @click="show = false;"
                                class="modal-close text-[#414651] dark:border-slate-600 dark:text-slate-500 p-2.5 text-center w-full border-[1.5px] border-[#D5D7DA] rounded-lg shadow-sm">
                                @lang('translations.cancel')
                            </button>

                            <button wire:click="addUpdateVehicle" wire:loading.attr="disabled"
                                wire:target="addUpdateVehicle" type="button"
                                class="text-white p-2.5 text-center w-full bg-primary rounded-lg shadow-sm hover:bg-primaryDark transition-all duration-300">
                                <span wire:loading.remove wire:target="addUpdateVehicle"> @lang('translations.save')
                                </span>
                                <div wire:loading wire:target="addUpdateVehicle">
                                    <div class="dot-spinner h-[1.4rem!important] w-[1.4rem!important]">
                                        <div class="dot-spinner__dot"></div>
                                        <div class="dot-spinner__dot"></div>
                                        <div class="dot-spinner__dot"></div>
                                        <div class="dot-spinner__dot"></div>
                                        <div class="dot-spinner__dot"></div>
                                        <div class="dot-spinner__dot"></div>
                                        <div class="dot-spinner__dot"></div>
                                        <div class="dot-spinner__dot"></div>
                                    </div>
                                </div>
                            </button>
                        </div>
                    </div>

                </div>
            </x-slot:body>
        </x-modal>
    @endcanany

    @can('vehicle_delete')
        <x-modal name="delete-record-modal">
            <x-slot:body>

                <div class="flex items-center justify-between">
                    <img class="size-10" src="{{ asset('assets/images/icons/delete.svg') }}" alt="delete">

                    <button @click="show = false" class="ms-auto">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                            stroke="#808080" class="w-6 h-6 cursor-pointer">
                            <path stroke-linecap="round" stroke-linejoin="round"
                                d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </button>

                </div>

                <div class="mt-4">
                    <h3 class="font-medium dark:text-slate-200">
                        @lang('translations.confirm_delete_record')
                    </h3>

                    <p class="text-sm mt-2 text-[#535862]da rk:text-slate-300 ">
                        @lang('translations.delete_warning')
                    </p>


                    <div class="flex items-center justify-center gap-5 mt-8 text-sm">
                        <button @click="show = false"
                            class="text-[#414651] p-2.5 text-center w-full border-[1.5px] border-[#D5D7DA] rounded-lg shadow-sm dark:text-slate-400 dark:border-slate-600">
                            @lang('translations.cancel')
                        </button>
                        <button wire:click="deleteRecord"
                            class="text-white p-2.5 text-center w-full bg-red-600 rounded-lg shadow-sm">
                            @lang('translations.delete')
                        </button>
                    </div>
                </div>


            </x-slot:body>
        </x-modal>
    @endcan

    @can('vehicle_view')
        <x-modal name="show-record">
            <x-slot:body>
                <!-- Modal Header -->
                <div class="flex items-center justify-between">
                    <h2 class="col-span-2 text-lg font-semibold text-center">
                        @lang('translations.vehicle_details')
                    </h2>

                    <button @click="show = false;" class="ms-auto">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                            stroke="#808080" class="w-6 h-6 cursor-pointer">
                            <path stroke-linecap="round" stroke-linejoin="round"
                                d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </button>
                </div>

                <div class="w-full">

                    <div class="mt-4">

                        <div class="grid gap-4 md:grid-cols-2">
                            @if ($selectedRecord)
                                <div class="col-span-2 md:col-span-1">
                                    <label class="text-sm text-gray-600 dark:text-slate-200">@lang('translations.device_imei')</label>
                                    <p class="text-sm text-gray-800 dark:text-slate-300">
                                        {{ $selectedRecord->imei }}</p>
                                </div>

                                <div class="col-span-2 md:col-span-1">
                                    <label class="text-sm text-gray-600 dark:text-slate-200">@lang('translations.license_plate')</label>
                                    <p class="text-sm text-gray-800 dark:text-slate-300">
                                        {{ $selectedRecord->license_plate }}</p>
                                </div>
                                <div class="col-span-2 md:col-span-1">
                                    <label class="text-sm text-gray-600 dark:text-slate-200">@lang('translations.driver_name')</label>
                                    <p class="text-sm text-gray-800 dark:text-slate-300">
                                        {{ $selectedRecord->driver?->name ?? 'N/A' }}</p>
                                </div>

                                <div class="col-span-2 md:col-span-1">
                                    <label class="text-sm text-gray-600 dark:text-slate-200">@lang('translations.model')</label>
                                    <p class="text-sm text-gray-800 dark:text-slate-300">{{ $selectedRecord->model }}
                                    </p>
                                </div>

                                <div class="col-span-2 md:col-span-1">
                                    <label class="text-sm text-gray-600 dark:text-slate-200">@lang('translations.mileage')</label>
                                    <p class="text-sm text-gray-800 dark:text-slate-300">
                                        {{ $selectedRecord->mileage }}
                                    </p>
                                </div>

                                <div class="col-span-2 md:col-span-1">
                                    <label class="text-sm text-gray-600 dark:text-slate-200">@lang('translations.year_of_registration')</label>
                                    <p class="text-sm text-gray-800 dark:text-slate-300">
                                        {{ $selectedRecord->year_of_registration }}</p>
                                </div>

                                <div class="col-span-2 md:col-span-1">
                                    <label class="text-sm text-gray-600 dark:text-slate-200">VIN</label>
                                    <p class="text-sm text-gray-800 dark:text-slate-300">{{ $selectedRecord->vin }}
                                    </p>

                                </div>

                                <div class="col-span-2 md:col-span-1">
                                    <label class="text-sm text-gray-600 dark:text-slate-200">@lang('translations.type')</label>
                                    <p
                                        class="flex items-center gap-2 text-sm text-gray-800 capitalize dark:text-slate-300">

                                        {{ $selectedRecord->type }}

                                        </a>
                                    </p>
                                </div>

                                <div class="col-span-2 md:col-span-1">
                                    <label class="text-sm text-gray-600 dark:text-slate-200">@lang('translations.icon')</label>
                                    <p
                                        class="flex items-center gap-2 text-sm text-gray-800 capitalize dark:text-slate-300">

                                        <img class="size-6"
                                            src="{{ asset('assets/images/icons/' . ($selectedRecord->icon ?? 'default') . '.svg') }}"
                                            alt="Car">
                                        </a>
                                    </p>
                                </div>

                                <div class="col-span-2 md:col-span-1">
                                    <label class="text-sm text-gray-600 dark:text-slate-200">@lang('translations.status')</label>
                                    <p class="mt-2 text-sm text-gray-800 dark:text-slate-300">
                                        @if ($selectedRecord->status == 1)
                                            <span
                                                class="px-2 py-0.5 font-medium leading-tight text-emerald-700 bg-emerald-100 rounded-full">
                                                @lang('translations.active') </span>
                                        @else
                                            <span
                                                class="px-2 py-0.5 font-medium leading-tight text-rose-700 bg-rose-100 rounded-full">
                                                @lang('translations.inactive') </span>
                                        @endif
                                    </p>
                                </div>

                                <div class="col-span-2 md:col-span-1">
                                    <label class="text-sm text-gray-600 dark:text-slate-200">@lang('translations.updated_at')</label>
                                    <p class="text-sm text-gray-800 dark:text-slate-300">
                                        @if ($selectedRecord->updated_at)
                                            {{ $selectedRecord->updated_at->format('d-m-Y H:i') }}
                                        @else
                                            N/A
                                        @endif
                                    </p>
                                </div>



                                <div class="col-span-2 md:col-span-1">
                                    <label class="text-sm text-gray-600 dark:text-slate-200">@lang('translations.created_at')</label>
                                    <p class="text-sm text-gray-800 dark:text-slate-300">
                                        {{ $selectedRecord->created_at->format('d-m-Y H:i') }}</p>
                                </div>
                            @else
                                <p>@lang('translations.no_vehicle_found')</p>
                            @endif
                        </div>


                    </div>

                </div>
            </x-slot:body>
        </x-modal>
    @endcan


    <x-modal name="show-vehicle-events">
        <x-slot:body>
            <!-- Modal Header -->
            <div class="flex items-center justify-between">
                <h2 class="col-span-2 text-lg font-semibold text-center">
                    @lang('translations.vehicle_events')
                </h2>

                <button @click="show = false;" class="ms-auto text-slate-600 dark:text-slate-400">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                        stroke="currentColor" class="w-6 h-6 cursor-pointer">
                        <path stroke-linecap="round" stroke-linejoin="round"
                            d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </button>
            </div>

            <div class="w-full">

                <div class="mt-4">

                    @if ($vehicleEvents)
                        <div class="w-full overflow-hidden">
                            @can('vehicle_events_management')
                                <div class="flex justify-end">
                                    <button wire:click="manageEvent"
                                        class="flex items-center justify-center px-5 py-2.5 text-sm tracking-wide text-white transition-colors duration-200 bg-primary rounded-lg shrink-0 sm:w-auto gap-x-2 hover:bg-primaryDark ">
                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                            stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
                                            <path stroke-linecap="round" stroke-linejoin="round"
                                                d="M12 9v6m3-3H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>

                                        <span>@lang('translations.add_event')</span>
                                    </button>
                                </div>
                            @endcan
                            <div class="mt-4 overflow-x-auto">
                                <div
                                    class="max-w-full rounded-lg shadow lg:flex lg:h-full lg:flex-col bg-gray-50 dark:bg-slate-900">
                                    <header
                                        class="flex items-center px-6 py-4 border-b border-gray-200 dark:border-slate-500 md:justify-between lg:flex-none">
                                        <div class="flex items-center gap-4 dark:text-slate-200 text-slate-800">
                                            <svg xmlns="http://www.w3.org/2000/svg" fill="none"
                                                viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"
                                                class="md:size-6 size-4">
                                                <path stroke-linecap="round" stroke-linejoin="round"
                                                    d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5m-9-6h.008v.008H12v-.008ZM12 15h.008v.008H12V15Zm0 2.25h.008v.008H12v-.008ZM9.75 15h.008v.008H9.75V15Zm0 2.25h.008v.008H9.75v-.008ZM7.5 15h.008v.008H7.5V15Zm0 2.25h.008v.008H7.5v-.008Zm6.75-4.5h.008v.008h-.008v-.008Zm0 2.25h.008v.008h-.008V15Zm0 2.25h.008v.008h-.008v-.008Zm2.25-4.5h.008v.008H16.5v-.008Zm0 2.25h.008v.008H16.5V15Z" />
                                            </svg>

                                            <h2 class="text-sm font-semibold leading-6 md:text-base">
                                                <time>{{ \Carbon\Carbon::create($currentYear, $currentMonth)->format('F Y') }}</time>
                                            </h2>
                                        </div>
                                        <div class="flex items-center ms-3 md:ms-0">
                                            <div
                                                class="relative flex items-center bg-white rounded-md shadow-sm dark:bg-slate-800 md:items-stretch">
                                                <button wire:click="previousMonth" type="button"
                                                    class="flex items-center justify-center w-12 pr-1 text-gray-400 border-l border-gray-300 dark:border-slate-500 h-9 rounded-l-md border-y hover:text-gray-500 focus:relative md:w-9 md:pr-0 md:hover:bg-gray-50 dark:hover:bg-slate-900 dark:hover:text-slate-300">
                                                    <span class="sr-only">Previous month</span>
                                                    <svg class="w-5 h-5" viewBox="0 0 20 20" fill="currentColor"
                                                        aria-hidden="true">
                                                        <path fill-rule="evenodd"
                                                            d="M12.79 5.23a.75.75 0 01-.02 1.06L8.832 10l3.938 3.71a.75.75 0 11-1.04 1.08l-4.5-4.25a.75.75 0 010-1.08l4.5-4.25a.75.75 0 011.06.02z"
                                                            clip-rule="evenodd" />
                                                    </svg>
                                                </button>

                                                <span class="relative w-px h-5 -mx-px bg-gray-300 md:hidden"></span>
                                                <button wire:click="nextMonth" type="button"
                                                    class="flex items-center justify-center w-12 pl-1 text-gray-400 border-r border-gray-300 h-9 rounded-r-md border-y hover:text-gray-500 dark:border-slate-500 focus:relative md:w-9 md:pl-0 md:hover:bg-gray-50 dark:hover:bg-slate-900 dark:hover:text-slate-300">
                                                    <span class="sr-only">Next month</span>
                                                    <svg class="w-5 h-5" viewBox="0 0 20 20" fill="currentColor"
                                                        aria-hidden="true">
                                                        <path fill-rule="evenodd"
                                                            d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z"
                                                            clip-rule="evenodd" />
                                                    </svg>
                                                </button>
                                            </div>
                                        </div>
                                    </header>
                                    <div
                                        class="shadow ring-1 ring-black ring-opacity-5 lg:flex lg:flex-auto lg:flex-col">

                                        <div
                                            class="flex text-xs leading-6 text-gray-700 bg-gray-200 dark:bg-slate-700 lg:flex-auto min-w-max">
                                            <div class="grid w-full grid-cols-7 grid-rows-6 gap-px">
                                                <div
                                                    class="flex items-center justify-center py-2 font-medium bg-gray-50 dark:bg-slate-800 dark:text-slate-100">
                                                    <span>M</span>
                                                    <span class="sr-only sm:not-sr-only">on</span>
                                                </div>
                                                <div
                                                    class="flex items-center justify-center py-2 font-medium bg-gray-50 dark:bg-slate-800 dark:text-slate-100">
                                                    <span>T</span>
                                                    <span class="sr-only sm:not-sr-only">ue</span>
                                                </div>
                                                <div
                                                    class="flex items-center justify-center py-2 font-medium bg-gray-50 dark:bg-slate-800 dark:text-slate-100">
                                                    <span>W</span>
                                                    <span class="sr-only sm:not-sr-only">ed</span>
                                                </div>
                                                <div
                                                    class="flex items-center justify-center py-2 font-medium bg-gray-50 dark:bg-slate-800 dark:text-slate-100">
                                                    <span>T</span>
                                                    <span class="sr-only sm:not-sr-only">hu</span>
                                                </div>
                                                <div
                                                    class="flex items-center justify-center py-2 font-medium bg-gray-50 dark:bg-slate-800 dark:text-slate-100">
                                                    <span>F</span>
                                                    <span class="sr-only sm:not-sr-only">ri</span>
                                                </div>
                                                <div
                                                    class="flex items-center justify-center py-2 font-medium bg-gray-50 dark:bg-slate-800 dark:text-slate-100">
                                                    <span>S</span>
                                                    <span class="sr-only sm:not-sr-only">at</span>
                                                </div>
                                                <div
                                                    class="flex items-center justify-center py-2 font-medium bg-gray-50 dark:bg-slate-800 dark:text-slate-100">
                                                    <span>S</span>
                                                    <span class="sr-only sm:not-sr-only">un</span>
                                                </div>

                                                @for ($i = 1; $i < $startDay; $i++)
                                                    <div
                                                        class="relative flex-shrink-0 px-3 py-2 text-gray-500 bg-gray-50 grow dark:bg-slate-800 dark:text-slate-200">
                                                        <time></time>
                                                    </div>
                                                @endfor
                                                @for ($day = 1; $day <= $daysInMonth; $day++)
                                                    @php
                                                        $currentDate = Carbon\Carbon::create(
                                                            $currentYear,
                                                            $currentMonth,
                                                            $day,
                                                        )->format('Y-m-d');
                                                        $eventsForDay = $vehicleEvents->filter(function ($event) use (
                                                            $currentDate,
                                                        ) {
                                                            return Carbon\Carbon::parse($event->start_date)->format(
                                                                'Y-m-d',
                                                            ) <= $currentDate &&
                                                                Carbon\Carbon::parse($event->end_date)->format(
                                                                    'Y-m-d',
                                                                ) >= $currentDate;
                                                        });
                                                    @endphp

                                                    <div
                                                        class="w-full px-3 py-2 overflow-y-scroll bg-white max-h-32 dark:bg-slate-900 dark:text-slate-200">
                                                        <time>{{ $day }}</time>

                                                        <!-- Show event details -->
                                                        @if ($eventsForDay->isNotEmpty())
                                                            <div class="mt-2">
                                                                @foreach ($eventsForDay as $event)
                                                                    @php
                                                                        $bgColor = 'bg-primary';
                                                                        if (
                                                                            $event->is_under_maintenance &&
                                                                            $event->maintenance_status
                                                                        ) {
                                                                            if (
                                                                                $event->maintenance_status == 'pending'
                                                                            ) {
                                                                                $bgColor = 'bg-yellow-500';
                                                                            } elseif (
                                                                                $event->is_under_maintenance &&
                                                                                $event->maintenance_status ==
                                                                                    'in progress'
                                                                            ) {
                                                                                $bgColor = 'bg-sky-500';
                                                                            } elseif (
                                                                                $event->is_under_maintenance &&
                                                                                $event->maintenance_status ==
                                                                                    'completed'
                                                                            ) {
                                                                                $bgColor = 'bg-emerald-500';
                                                                            }
                                                                        }
                                                                    @endphp

                                                                    <div @can('vehicle_events_management')
                                                                        wire:click="manageEvent({{ $event->id }})"
                                                                    @endcan
                                                                        class="px-2 py-1 text-xs {{ $bgColor }} text-white rounded mb-1 cursor-pointer select-none">
                                                                        <strong>
                                                                            @if ($event->event_type == 'Other')
                                                                                {{ $event->other_event_type }}
                                                                            @else
                                                                                {{ $event->event_type }}
                                                                            @endif
                                                                        </strong>
                                                                    </div>
                                                                @endforeach
                                                            </div>
                                                        @endif
                                                    </div>
                                                @endfor




                                                {{-- End days to complete the last week --}}
                                                @for ($i = 1; $i < $endDay; $i++)
                                                    <div
                                                        class="relative px-3 py-2 text-gray-500 bg-gray-50 dark:bg-slate-800 dark:text-slate-200">
                                                        <time></time>
                                                    </div>
                                                @endfor

                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>
                    @else
                        <p>@lang('translations.no_vehicle_events_found')</p>
                    @endif
                </div>

            </div>
        </x-slot:body>
    </x-modal>


    <x-modal name="manage-event">
        <x-slot:body>
            <!-- Modal Header -->
            <div class="flex items-center justify-between">

                <button @click="show = false;" class="ms-auto text-slate-600 dark:text-slate-400">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                        stroke="currentColor" class="w-6 h-6 cursor-pointer">
                        <path stroke-linecap="round" stroke-linejoin="round"
                            d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </button>
            </div>

            <div class="w-full">

                <div class="flex items-center justify-center">
                    <h2 class="text-xl font-medium text-center text-black dark:text-slate-100">
                        @if ($recordId)
                            @lang('translations.edit_event')
                        @else
                            @lang('translations.add_event')
                        @endif
                    </h2>
                </div>


                <div class="mt-4">

                    <div class="grid grid-cols-2 gap-4">
                        <div class="col-span-2 md:col-span-1">
                            <label class="text-sm text-[#414651] dark:text-slate-300">@lang('translations.event_type')</label>

                            <div class="relative mt-2">
                                <select wire:model.live="event_type" id="event_type"
                                    class="peer py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm rounded-lg text-sm focus:border-primary outline-none transition-all duration-300 dark:text-slate-300 dark:bg-slate-800 dark:border-slate-600 dark:focus:border-primary">
                                    <option value="">@lang('translations.select')</option>
                                    <option value="Service">@lang('translations.service')</option>
                                    <option value="Tire Change">@lang('translations.tire_change')</option>
                                    <option value="Insurance Expiry">@lang('translations.insurance_expiry')</option>
                                    <option value="Registration Expiry">@lang('translations.registration_expiry')</option>
                                    <option value="Inspection">@lang('translations.inspection')</option>
                                    <option value="Fuel Refill">@lang('translations.fuel_refill')</option>
                                    <option value="Cleaning/Washing">@lang('translations.cleaning_washing')</option>
                                    <option value="Battery Check">@lang('translations.battery_check')</option>
                                    <option value="Brake Check">@lang('translations.brake_check')</option>
                                    <option value="Other Maintenance">@lang('translations.other_maintenance')</option>
                                    <option value="Under Maintenance">@lang('translations.under_maintenance')</option>
                                    <option value="Documentation Renewal">@lang('translations.documentation_renewal')</option>
                                    <option value="License Expiry">@lang('translations.license_expiry')</option>
                                    <option value="Tax Payment">@lang('translations.tax_payment')</option>
                                    <option value="Policy Update">@lang('translations.policy_update')</option>
                                    <option value="Deployment">@lang('translations.deployment')</option>
                                    <option value="Reserved">@lang('translations.reserved')</option>
                                    <option value="Decommissioned">@lang('translations.decommissioned')</option>
                                    <option value="Other">@lang('translations.other')</option>

                                </select>
                            </div>

                            @error('event_type')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>

                        @if ($event_type == 'Other')
                            <div class="col-span-2 md:col-span-1">
                                <label class="text-sm text-[#414651] dark:text-slate-300">@lang('translations.other_event_type')</label>

                                <div class="relative mt-2">
                                    <input type="text" wire:model="other_event_type" id="other_event_type"
                                        class="peer py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm rounded-lg text-sm focus:border-primary outline-none transition-all duration-300 dark:text-slate-300 dark:bg-slate-800 dark:border-slate-600 dark:focus:border-primary"
                                        placeholder="@lang('translations.enter') @lang('translations.other_event_type')">
                                </div>
                                @error('other_event_type')
                                    <div class="mt-2 text-xs text-red-500">
                                        {{ $message }}
                                    </div>
                                @enderror
                            </div>
                        @endif


                        <div class="col-span-2 md:col-span-1">
                            <label class="text-sm text-[#414651] dark:text-slate-300">@lang('translations.start_date')</label>

                            <div class="relative mt-2">
                                <input type="date" wire:model="start_date" id="start_date"
                                    class="peer py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm rounded-lg text-sm focus:border-primary outline-none transition-all duration-300 dark:text-slate-300 dark:bg-slate-800 dark:border-slate-600 dark:focus:border-primary"
                                    placeholder="@lang('translations.enter') @lang('translations.start_date')">
                            </div>

                            @error('start_date')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror

                        </div>

                        <div class="col-span-2 md:col-span-1">
                            <label class="text-sm text-[#414651] dark:text-slate-300">@lang('translations.end_date')</label>

                            <div class="relative mt-2">
                                <input type="date" wire:model="end_date" id="end_date"
                                    class="peer py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm rounded-lg text-sm focus:border-primary outline-none transition-all duration-300 dark:text-slate-300 dark:bg-slate-800 dark:border-slate-600 dark:focus:border-primary"
                                    placeholder="@lang('translations.enter') @lang('translations.end_date')">
                            </div>

                            @error('end_date')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror

                        </div>

                        <div class="flex items-center col-span-2">
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" checked="" wire:model.live="is_under_maintenance"
                                    id="is_under_maintenance" class="sr-only peer">
                                <div
                                    class="w-9 h-5 bg-gray-200 hover:bg-gray-300 peer-focus:outline-0 peer-focus:ring-transparent rounded-full peer transition-all ease-in-out duration-500 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-primary hover:peer-checked:bg-primary dark:bg-slate-400">
                                </div>
                            </label>

                            <label for="is_under_maintenance"
                                class="ms-2 cursor-pointer select-none text-sm text-[#414651] dark:text-slate-300">
                                @lang('translations.under_maintenance')
                            </label>
                        </div>

                        @if ($is_under_maintenance)
                            <div class="col-span-2">
                                <label class="text-sm text-[#414651] dark:text-slate-300">@lang('translations.maintenance_status')</label>

                                <div class="relative mt-2">
                                    <select wire:model.live="maintenance_status" id="maintenance_status"
                                        class="peer py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm rounded-lg text-sm focus:border-primary outline-none transition-all duration-300 dark:text-slate-300 dark:bg-slate-800 dark:border-slate-600 dark:focus:border-primary">
                                        <option value="">@lang('translations.select')</option>
                                        <option value="pending">@lang('translations.pending')</option>
                                        <option value="in progress">@lang('translations.in_progress')</option>
                                        <option value="completed">@lang('translations.completed')</option>
                                    </select>
                                </div>

                                @error('maintenance_status')
                                    <div class="mt-2 text-xs text-red-500">
                                        {{ $message }}
                                    </div>
                                @enderror
                            </div>
                        @endif

                        @can('manage_vehicle_event_users')
                            <div class="flex items-center col-span-2">
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" checked="" wire:model.live="has_alert" id="has_alert"
                                        class="sr-only peer">
                                    <div
                                        class="w-9 h-5 bg-gray-200 hover:bg-gray-300 peer-focus:outline-0 peer-focus:ring-transparent rounded-full peer transition-all ease-in-out duration-500 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-primary hover:peer-checked:bg-primary dark:bg-slate-400">
                                    </div>
                                </label>

                                <label for="has_alert"
                                    class="ms-2 cursor-pointer select-none text-sm text-[#414651] dark:text-slate-300">
                                    @lang('translations.has_alert')
                                </label>
                            </div>

                            @if ($has_alert)
                                <div class="col-span-2">
                                    <label
                                        class="text-sm text-[#414651] dark:text-slate-300">@lang('translations.select_users')</label>


                                    <livewire:components.multi-select-dropdown :placeholder="__('translations.search_users')"
                                        field-name="selectedUsers" fetch-method="getUsers" :selectedOptions="$alert_recipients" />

                                    @error('selectedUsers')
                                        <div class="mt-2 text-xs text-red-500">
                                            {{ $message }}
                                        </div>
                                    @enderror

                                </div>

                                <div class="col-span-2">
                                    <label
                                        class="text-sm text-[#414651] dark:text-slate-300">@lang('translations.notifications')</label>

                                    <label class="flex items-center gap-2 mt-2">
                                        <input type="checkbox" value="1" wire:model="alert_type"
                                            id="alert_type-1" class="accent-primary">
                                        @lang('translations.email_notifications')
                                    </label>

                                    <label class="flex items-center gap-2 mt-2">
                                        <input type="checkbox" value="2" wire:model="alert_type"
                                            id="alert_type-2" class="accent-primary">
                                        @lang('translations.push_notifications')

                                    </label>

                                    @error('alert_type')
                                        <div class="mt-2 text-xs text-red-500">
                                            {{ $message }}
                                        </div>
                                    @enderror
                                </div>
                            @endif
                        @endcan



                        <div class="col-span-2">
                            <label class="text-sm text-[#414651] dark:text-slate-300">@lang('translations.event_description')</label>

                            <div class="relative mt-2">
                                <textarea wire:model="description" id="description"
                                    class="peer py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm rounded-lg text-sm focus:border-primary outline-none transition-all duration-300 dark:text-slate-300 dark:bg-slate-800 dark:border-slate-600 dark:focus:border-primary"
                                    placeholder="@lang('translations.enter') @lang('translations.description')"></textarea>
                            </div>
                            @error('description')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>
                        <div class="col-span-2">
                            <label class="text-sm text-[#414651] dark:text-slate-300">@lang('translations.attachments')</label>

                            <div class="relative mt-2">
                                <input type="file" multiple wire:model="attachments" id="attachments"
                                    class="peer py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm rounded-lg text-sm focus:border-primary outline-none transition-all duration-300 dark:text-slate-300 dark:bg-slate-800 dark:border-slate-600 dark:focus:border-primary">
                            </div>

                            @if ($attachments && count($attachments) > 0)
                                <div class="flex items-center gap-2 mt-2 text-slate-700 dark:text-slate-200">
                                    <img class="size-5" src="{{ asset('assets/images/icons/document.svg') }}"
                                        alt="document">

                                    <p>@lang('translations.documents_attached', ['documents' => count($attachments)])</p>
                                </div>
                            @endif

                            @if ($old_attachments)
                                <div class="mt-5">
                                    <h3 class="text-lg font-medium text-slate-800 dark:text-slate-200">
                                        @lang('translations.added_attachments')</h3>
                                    @foreach ($old_attachments as $index => $old_attachment)
                                        <div class="flex items-center justify-between gap-2">
                                            <a class="flex items-center gap-2 mt-2 text-slate-700 dark:text-slate-300"
                                                target="_blank"
                                                href="{{ asset('storage/' . $old_attachment['file_path'] ?? '') }}">
                                                <img class="size-5"
                                                    src="{{ asset('assets/images/icons/document.svg') }}"
                                                    alt="document">
                                                {{ $old_attachment['original_file_name'] }}
                                            </a>

                                            <!-- Delete Button with Tooltip -->
                                            <div x-data="{ showTooltip: false }" class="relative flex-shrink-0">
                                                <button wire:click="deleteAttachmentConfirmation({{ $index }})"
                                                    @mouseenter="showTooltip = true"
                                                    @mouseleave="showTooltip = false">
                                                    <img class="size-5"
                                                        src="{{ asset('assets/images/icons/delete.svg') }}"
                                                        alt="Delete">
                                                </button>
                                                <div x-show="showTooltip"
                                                    x-transition:enter="transition ease-out duration-200"
                                                    x-transition:enter-start="opacity-0 transform scale-95"
                                                    x-transition:enter-end="opacity-100 transform scale-100"
                                                    x-transition:leave="transition ease-in duration-150"
                                                    x-transition:leave-start="opacity-100 transform scale-100"
                                                    x-transition:leave-end="opacity-0 transform scale-95"
                                                    class="absolute right-0 px-3 py-1 mb-2 text-xs text-white bg-gray-800 rounded shadow-lg bottom-full w-max"
                                                    style="display: none;">
                                                    @lang('translations.delete')
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            @endif
                            @error('attachments')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>
                    </div>

                    <div class="flex flex-wrap items-center justify-center gap-5 mt-8 text-sm md:flex-nowrap grow">
                        @if ($recordId)
                            <button wire:confirm="@lang('translations.confirm_delete_record')" wire:click="deleteEvent"
                                class="modal-close text-red-500 dark:border-red-400 dark:text-red-500 p-2.5 text-center w-full border-[1.5px] border-red-400 rounded-lg">
                                @lang('translations.delete')
                            </button>
                        @endif

                        <button @click="show = false;"
                            class="modal-close text-[#414651] dark:border-slate-600 dark:text-slate-500 p-2.5 text-center w-full border-[1.5px] border-[#D5D7DA] rounded-lg shadow-sm">
                            @lang('translations.cancel')
                        </button>

                        <button wire:click="addUpdateVehicleEvent" wire:loading.attr="disabled"
                            wire:target="addUpdateVehicleEvent" type="button"
                            class="text-white p-2.5 text-center w-full bg-primary rounded-lg shadow-sm hover:bg-primaryDark transition-all duration-300">
                            <span wire:loading.remove wire:target="addUpdateVehicleEvent"> @lang('translations.save')
                            </span>
                            <div wire:loading wire:target="addUpdateVehicleEvent">
                                <div class="dot-spinner h-[1.4rem!important] w-[1.4rem!important]">
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                </div>
                            </div>
                        </button>
                    </div>
                </div>

            </div>
        </x-slot:body>
    </x-modal>

    <x-modal name="vehicle-users">
        <x-slot:body>
            <!-- Modal Header -->
            <div class="w-full">

                <!-- Modal Header -->
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-lg font-semibold">@lang('translations.vehicle_users')</h2>
                    <button @click="show = false;" class="ms-auto">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                            stroke-width="1.5" stroke="#808080" class="w-6 h-6 cursor-pointer">
                            <path stroke-linecap="round" stroke-linejoin="round"
                                d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </button>
                </div>

                <div class="w-full">
                    <div class="flex items-center justify-end">
                        <button wire:click="addVehicleUser" type="button"
                            class="text-white p-2.5 px-4 text-center w-fit ms-auto bg-primary rounded-lg shadow-sm hover:bg-secondary transition-all duration-300">
                            @lang('translations.add_vehicle_user')
                        </button>
                    </div>
                    @if ($vehicleUsers)
                        <div class="mt-2 overflow-x-auto">
                            <table
                                class="min-w-full text-sm bg-white dark:bg-slate-900 dark:text-slate-100 text-slate-800">
                                <thead>
                                    <tr>
                                        <th
                                            class="px-4 py-2 text-xs leading-4 tracking-wider text-left text-gray-600 uppercase bg-gray-100 border-b border-gray-200 dark:text-slate-200 dark:bg-slate-700 whitespace-nowrap">
                                            @lang('translations.name')</th>
                                        <th
                                            class="px-4 py-2 text-xs leading-4 tracking-wider text-left text-gray-600 uppercase bg-gray-100 border-b border-gray-200 dark:text-slate-200 dark:bg-slate-700 whitespace-nowrap">
                                            @lang('translations.vehicle')</th>
                                        <th
                                            class="px-4 py-2 text-xs leading-4 tracking-wider text-left text-gray-600 uppercase bg-gray-100 border-b border-gray-200 dark:text-slate-200 dark:bg-slate-700 whitespace-nowrap">
                                            @lang('translations.assigned_at')</th>
                                        <th
                                            class="px-4 py-2 text-xs leading-4 tracking-wider text-gray-600 uppercase bg-gray-100 border-b border-gray-200 dark:text-slate-200 dark:bg-slate-700 whitespace-nowrap text-end">
                                            @lang('translations.actions')</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse ($vehicleUsers?->vehicleUsers as $vehicleUser)
                                        <tr>
                                            <td class="px-4 py-2 border-b border-gray-200">
                                                {{ $vehicleUser?->user?->name ?? 'N/A' }}
                                                <div class="text-xs text-gray-500">
                                                    {{ $vehicleUser?->user?->email ?? 'N/A' }}
                                                </div>

                                            </td>
                                            <td class="px-4 py-2 border-b border-gray-200">
                                                {{ $vehicleUsers->license_plate ?? 'N/A' }}
                                            </td>
                                            <td class="px-4 py-2 border-b border-gray-200">
                                                {{ $vehicleUser->created_at->format('d/m/Y H:i') ?? 'N/A' }}
                                            </td>
                                            <td class="px-4 py-2 border-b border-gray-200 ">

                                                <div class="flex items-center justify-end gap-1">
                                                    <!-- Delete Button with Tooltip -->
                                                    <div x-data="{ showTooltip: false }" class="relative flex-shrink-0">
                                                        <button
                                                            wire:click="deleteVehicleUserConfirmation({{ $vehicleUser->id }})"
                                                            @mouseenter="showTooltip = true"
                                                            @mouseleave="showTooltip = false">
                                                            <img class="size-5"
                                                                src="{{ asset('assets/images/icons/delete.svg') }}"
                                                                alt="Delete">
                                                        </button>
                                                        <div x-show="showTooltip"
                                                            x-transition:enter="transition ease-out duration-200"
                                                            x-transition:enter-start="opacity-0 transform scale-95"
                                                            x-transition:enter-end="opacity-100 transform scale-100"
                                                            x-transition:leave="transition ease-in duration-150"
                                                            x-transition:leave-start="opacity-100 transform scale-100"
                                                            x-transition:leave-end="opacity-0 transform scale-95"
                                                            class="absolute right-0 px-3 py-1 mb-2 text-xs text-white bg-gray-800 rounded shadow-lg bottom-full w-max"
                                                            style="display: none;">
                                                            @lang('translations.delete')
                                                        </div>
                                                    </div>
                                                </div>

                                            </td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td colspan="4" class="p-3 text-center">
                                                @lang('translations.no_user_found')
                                            </td>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    @else
                        <p class="text-center">@lang('translations.no_user_found')</p>
                    @endif


                </div>

            </div>
        </x-slot:body>
    </x-modal>


    <x-modal name="add-vehicle-user">
        <x-slot:body>
            <!-- Modal Header -->
            <div class="w-full">

                <!-- Modal Header -->
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-lg font-semibold">@lang('translations.add_vehicle_user')</h2>
                    <button @click="show = false;" class="ms-auto">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                            stroke-width="1.5" stroke="#808080" class="w-6 h-6 cursor-pointer">
                            <path stroke-linecap="round" stroke-linejoin="round"
                                d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </button>
                </div>

                <div class="w-full">
                    <div class="col-span-2">
                        <label class="text-sm text-[#414651] dark:text-slate-300">@lang('translations.select_users')</label>


                        <livewire:components.select-dropdown :placeholder="__('translations.search_users')" field-name="vehicleUser"
                            fetch-method="getUsers" />

                        @error('vehicleUser')
                            <div class="mt-2 text-xs text-red-500">
                                {{ $message }}
                            </div>
                        @enderror
                    </div>

                </div>

                <div class="flex items-center justify-center gap-5 mt-8 text-sm">
                    <button @click="show = false;"
                        class="modal-close text-[#414651] dark:border-slate-600 dark:text-slate-500 p-2.5 text-center w-full border-[1.5px] border-[#D5D7DA] rounded-lg shadow-sm">
                        @lang('translations.cancel')
                    </button>

                    <button wire:click="assignVehicleUser" wire:loading.attr="disabled"
                        wire:target="assignVehicleUser" type="button"
                        class="text-white p-2.5 text-center w-full bg-primary rounded-lg shadow-sm hover:bg-primaryDark transition-all duration-300">
                        <span wire:loading.remove wire:target="assignVehicleUser"> @lang('translations.save')
                        </span>
                        <div wire:loading wire:target="assignVehicleUser">
                            <div class="dot-spinner h-[1.4rem!important] w-[1.4rem!important]">
                                <div class="dot-spinner__dot"></div>
                                <div class="dot-spinner__dot"></div>
                                <div class="dot-spinner__dot"></div>
                                <div class="dot-spinner__dot"></div>
                                <div class="dot-spinner__dot"></div>
                                <div class="dot-spinner__dot"></div>
                                <div class="dot-spinner__dot"></div>
                                <div class="dot-spinner__dot"></div>
                            </div>
                        </div>
                    </button>
                </div>

            </div>
        </x-slot:body>
    </x-modal>

    <x-modal name="delete-vehicle-user-record-modal">
        <x-slot:body>

            <div class="flex items-center justify-between">
                <img class="size-10" src="{{ asset('assets/images/icons/delete.svg') }}" alt="delete">

                <button @click="show = false" class="ms-auto">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                        stroke="#808080" class="w-6 h-6 cursor-pointer">
                        <path stroke-linecap="round" stroke-linejoin="round"
                            d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </button>

            </div>

            <div class="mt-4">
                <h3 class="font-medium dark:text-slate-200">
                    @lang('translations.confirm_delete_record')
                </h3>

                <p class="text-sm mt-2 text-[#535862]da rk:text-slate-300 ">
                    @lang('translations.delete_warning')
                </p>


                <div class="flex items-center justify-center gap-5 mt-8 text-sm">
                    <button @click="show = false"
                        class="text-[#414651] p-2.5 text-center w-full border-[1.5px] border-[#D5D7DA] rounded-lg shadow-sm dark:text-slate-400 dark:border-slate-600">
                        @lang('translations.cancel')
                    </button>
                    <button wire:click="deleteVehicleUser"
                        class="text-white p-2.5 text-center w-full bg-red-600 rounded-lg shadow-sm">
                        @lang('translations.delete')
                    </button>
                </div>
            </div>


        </x-slot:body>
    </x-modal>

    <x-modal name="delete-attachment-record-modal">
        <x-slot:body>

            <div class="flex items-center justify-between">
                <img class="size-10" src="{{ asset('assets/images/icons/delete.svg') }}" alt="delete">

                <button @click="show = false" class="ms-auto">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                        stroke="#808080" class="w-6 h-6 cursor-pointer">
                        <path stroke-linecap="round" stroke-linejoin="round"
                            d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </button>

            </div>

            <div class="mt-4">
                <h3 class="font-medium dark:text-slate-200">
                    @lang('translations.confirm_delete_record')
                </h3>

                <p class="text-sm mt-2 text-[#535862]da rk:text-slate-300 ">
                    @lang('translations.delete_warning')
                </p>


                <div class="flex items-center justify-center gap-5 mt-8 text-sm">
                    <button @click="show = false"
                        class="text-[#414651] p-2.5 text-center w-full border-[1.5px] border-[#D5D7DA] rounded-lg shadow-sm dark:text-slate-400 dark:border-slate-600">
                        @lang('translations.cancel')
                    </button>
                    <button wire:click="deleteAttachment"
                        class="text-white p-2.5 text-center w-full bg-red-600 rounded-lg shadow-sm">
                        @lang('translations.delete')
                    </button>
                </div>
            </div>


        </x-slot:body>
    </x-modal>


</div>
