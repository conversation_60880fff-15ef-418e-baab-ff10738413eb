<div class="p-4 text-sm">

    <div>
        <h1 class="text-xl font-medium tracking-wide font-poppins text-slate-800 dark:text-slate-100">
            @lang('translations.dashboard')
        </h1>

        <p class="mt-2 text-slate-600 dark:text-slate-300">
            @lang('translations.monitor_manage_optimize_fleet')
        </p>
    </div>


    <div class="flex flex-col gap-4 p-1 mt-10 md:flex-row grow">
        <div class="w-full md:w-4/6">
            <div class="grid gap-3 p-1 md:grid-cols-3">
                <div class="w-full p-5 bg-white rounded-md shadow dark:bg-slate-800">
                    <img class="h-16" src="{{ asset('assets/images/icons/fleet.svg') }}" alt="fleet">
                    <h4 class="mt-2 text-slate-800 dark:text-slate-300">
                        @lang('translations.total_fleet_size')

                    </h4>
                    <p class="text-xl font-semibold dark:text-white">
                        {{ $totalVehicles }}

                    </p>
                </div>
                <div class="w-full p-5 bg-white rounded-md shadow dark:bg-slate-800">
                    <img class="h-16" src="{{ asset('assets/images/icons/vehicle-moving.svg') }}" alt="motion">
                    <h4 class="mt-2 text-slate-800 dark:text-slate-300">
                        @lang('translations.vehicles_in_motion')

                    </h4>
                    <p class="text-xl font-semibold dark:text-white">
                        {{ $movingCount ?? 0 }}
                    </p>
                </div>
                <div class="w-full p-5 bg-white rounded-md shadow dark:bg-slate-800">
                    <img class="h-16" src="{{ asset('assets/images/icons/stopped.svg') }}" alt="stopped">
                    <h4 class="mt-2 text-slate-800 dark:text-slate-300">
                        @lang('translations.vehicles_stopped')

                    </h4>
                    <p class="text-xl font-semibold dark:text-white">
                        {{ $stoppedCount ?? 0 }}

                    </p>
                </div>
                <div class="w-full p-5 bg-white rounded-md shadow dark:bg-slate-800">
                    <img class="h-16" src="{{ asset('assets/images/icons/fuel.svg') }}" alt="stopped">
                    <h4 class="mt-2 text-slate-800 dark:text-slate-300">
                        @lang('translations.total_fuel_used') (@lang('translations.CAN_vehicles'))
                    </h4>
                    <p class="text-xl font-semibold dark:text-white">
                        {{ round($totalFuel, 2) }} liters
                    </p>
                </div>
                <div class="w-full p-5 bg-white rounded-md shadow dark:bg-slate-800">
                    <img class="h-16" src="{{ asset('assets/images/icons/fuel-consumption.svg') }}" alt="stopped">
                    <h4 class="mt-2 text-slate-800 dark:text-slate-300">
                        @lang('translations.average_consumption') (@lang('translations.CAN_vehicles'))

                    </h4>
                    <p class="text-xl font-semibold dark:text-white">
                        {{ round($averageFuel, 3) }} l/100km
                    </p>
                </div>
                <a href="{{ route('drivers') }}" class="w-full p-5 bg-white rounded-md shadow dark:bg-slate-800">
                    <img class="h-16" src="{{ asset('assets/images/icons/drivers.svg') }}" alt="stopped">
                    <h4 class="mt-2 text-slate-800 dark:text-slate-300">
                        @lang('translations.available_drivers')

                    </h4>
                    <p class="text-xl font-semibold dark:text-white">
                        {{ $drivers }}
                    </p>
                </a>
                <a href="{{ route('vehicles') }}"
                    class="block w-full p-5 bg-white rounded-md shadow dark:bg-slate-800">
                    <img class="h-16" src="{{ asset('assets/images/icons/vehicles.svg') }}" alt="stopped">
                    <h4 class="mt-2 text-slate-800 dark:text-slate-300">
                        @lang('translations.vehicles_in_workshop')

                    </h4>
                    <p class="text-xl font-semibold dark:text-white">
                        {{ $vehiclesInMaintenance ?? 0 }}
                    </p>
                </a>
                <a href="{{ route('vehicles') }}"
                    class="block w-full p-5 bg-white rounded-md shadow md:col-span-2 dark:bg-slate-800">
                    <img class="h-16" src="{{ asset('assets/images/icons/fuel_anomalous.svg') }}" alt="stopped">
                    <h4 class="mt-2 text-slate-800 dark:text-slate-300">
                        @lang('translations.vehicles_with_anomalous')

                    </h4>
                    <p class="text-xl font-semibold dark:text-white">
                        {{ $anomalyVehicles ?? 0 }}
                    </p>
                </a>
            </div>


            <div class="flex flex-col mt-5">
                <div class="-m-1.5 w-full md:w-full md:overflow-x-auto overflow-x-scroll">
                    <div class="p-1.5 min-w-full inline-block align-middle">
                        <div class="overflow-hidden border rounded-lg shadow dark:border-slate-400">
                            <table class="min-w-full divide-y divide-gray-200 dark:divide-slate-400">
                                <thead class="bg-gray-100 dark:bg-slate-900">
                                    <tr>
                                        <th scope="col"
                                            class="px-6 py-3 text-xs font-medium text-gray-500 uppercase dark:text-slate-300 text-start">
                                            @lang('translations.license_plate')
                                        </th>

                                        <th scope="col"
                                            class="px-6 py-3 text-xs font-medium text-gray-500 uppercase dark:text-slate-300 text-start">
                                            @lang('translations.status')
                                        </th>
                                        <th scope="col"
                                            class="px-6 py-3 text-xs font-medium text-gray-500 uppercase dark:text-slate-300 text-start">
                                            @lang('translations.speed')
                                        </th>
                                        <th scope="col"
                                            class="px-6 py-3 text-xs font-medium text-gray-500 uppercase dark:text-slate-300 text-start">
                                            @lang('translations.driver_name')
                                        </th>
                                    </tr>
                                </thead>
                                <tbody class="divide-y divide-gray-200 bg-gray-50 dark:bg-slate-800">
                                    @foreach ($vehicles as $vehicle)
                                        <tr>
                                            <td
                                                class="px-6 py-4 text-sm font-medium text-gray-800 dark:text-slate-300 whitespace-nowrap">
                                                {{ $vehicle->license_plate }}
                                            </td>

                                            <td
                                                class="px-6 py-4 text-sm font-medium text-gray-800 dark:text-slate-300 whitespace-nowrap">
                                                @if ($vehicle->movement_status == 1 && $vehicle->ignition_status == 1 && $vehicle->speed > 0)
                                                    <div
                                                        class="px-3 py-1 text-xs font-medium text-center rounded-full shadow text-emerald-600 bg-emerald-100 w-fit">
                                                        @lang('translations.moving')
                                                    </div>
                                                @elseif ($vehicle->speed == 0 && $vehicle->ignition_status == 1)
                                                    <div
                                                        class="px-3 py-1 text-xs font-medium text-center text-yellow-600 bg-yellow-100 rounded-full shadow w-fit">
                                                        @lang('translations.stopped')
                                                    </div>
                                                @else
                                                    <div
                                                        class="px-3 py-1 text-xs font-medium text-center rounded-full shadow text-rose-600 bg-rose-100 w-fit">
                                                        @lang('translations.parked')

                                                    </div>
                                                @endif
                                            </td>
                                            <td
                                                class="px-6 py-4 text-sm font-medium text-gray-800 dark:text-slate-300 whitespace-nowrap">
                                                {{ $vehicle->speed }} km/h
                                            </td>
                                            <td
                                                class="px-6 py-4 text-sm font-medium text-gray-800 dark:text-slate-300 whitespace-nowrap">
                                                {{ $vehicle->driver?->name }}
                                            </td>
                                        </tr>
                                    @endforeach

                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>


        </div>
        <div class="w-full p-5 bg-white rounded-md shadow dark:bg-slate-800 md:w-2/6">
            <h3 class="text-lg font-semibold text-slate-800 dark:text-slate-100">
                @lang('translations.notifications')

            </h3>

            @if ($notifications->isEmpty())
                <p class="mt-4 text-lg text-slate-600 dark:text-slate-300">
                    @lang('translations.no_notifications_yet')
                </p>
            @else
                @foreach ($notifications as $notification)
                    <div class="p-5 border-b border-b-secondary dark:border-slate-500">
                        <div class="flex">
                            <div
                                class="flex items-center justify-center flex-shrink-0 w-8 h-8 overflow-hidden rounded-full dark:text-slate-200">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                    stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
                                    <path stroke-linecap="round" stroke-linejoin="round"
                                        d="M14.857 17.082a23.848 23.848 0 0 0 5.454-1.31A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6 9v.75a8.967 8.967 0 0 1-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 0 1-5.714 0m5.714 0a3 3 0 1 1-5.714 0M3.124 7.5A8.969 8.969 0 0 1 5.292 3m13.416 0a8.969 8.969 0 0 1 2.168 4.5" />
                                </svg>
                            </div>

                            <div class="ms-2">
                                <h3 class="text-sm font-semibold dark:text-slate-100">
                                    {{ $notification['title'] ?? '' }}
                                </h3>
                                <p class="mt-2 text-xs font-medium text-slate-600 dark:text-slate-400 dark:font-normal">
                                    {{ $notification['body'] ?? '' }}
                                </p>
                                <div class="mt-2 text-xs font-light text-slate-500 dark:text-slate-400">
                                    {{ $notification['created_at'] ?? '' }}
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
            @endif
        </div>

    </div>
</div>
